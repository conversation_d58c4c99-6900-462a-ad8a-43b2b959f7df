// Simple test file to verify the validation function
// This can be run in the browser console or with a test runner

// Test cases for the validation function
const testValidation = () => {
  // Validation function (copied from the component)
  const validateTextAreaContent = (text) => {
    if (!text || typeof text !== 'string') return { isValid: true, error: '' };
    
    // Check if there are spaces in the middle of words
    // This regex matches any word character followed by a space followed by another word character
    const hasSpaceInMiddle = /\w\s+\w/.test(text);
    
    if (hasSpaceInMiddle) {
      return {
        isValid: false,
        error: 'Spaces are not allowed in the middle of words'
      };
    }
    
    return { isValid: true, error: '' };
  };

  // Test cases
  const testCases = [
    // Should pass (no errors)
    { input: 'abc', expected: true, description: 'Single word' },
    { input: 'abcdef', expected: true, description: 'Single word without spaces' },
    { input: '', expected: true, description: 'Empty string' },
    { input: null, expected: true, description: 'Null value' },
    { input: undefined, expected: true, description: 'Undefined value' },
    { input: 'hello', expected: true, description: 'Simple word' },
    { input: 'hello-world', expected: true, description: 'Hyphenated word' },
    { input: 'hello_world', expected: true, description: 'Underscore word' },
    { input: 'hello123', expected: true, description: 'Word with numbers' },
    
    // Should fail (has errors)
    { input: 'abc def', expected: false, description: 'Two words with space' },
    { input: 'hello world', expected: false, description: 'Two words with space' },
    { input: 'a b', expected: false, description: 'Single letters with space' },
    { input: 'test ing', expected: false, description: 'Word split with space' },
    { input: 'hello world test', expected: false, description: 'Multiple words with spaces' },
    { input: '123 456', expected: false, description: 'Numbers with space' },
    { input: 'a b c', expected: false, description: 'Multiple single letters with spaces' },
  ];

  console.log('Running validation tests...\n');
  
  let passedTests = 0;
  let failedTests = 0;

  testCases.forEach((testCase, index) => {
    const result = validateTextAreaContent(testCase.input);
    const passed = result.isValid === testCase.expected;
    
    if (passed) {
      passedTests++;
      console.log(`✅ Test ${index + 1}: ${testCase.description} - PASSED`);
    } else {
      failedTests++;
      console.log(`❌ Test ${index + 1}: ${testCase.description} - FAILED`);
      console.log(`   Input: "${testCase.input}"`);
      console.log(`   Expected: ${testCase.expected ? 'valid' : 'invalid'}`);
      console.log(`   Got: ${result.isValid ? 'valid' : 'invalid'}`);
      if (!result.isValid) {
        console.log(`   Error: ${result.error}`);
      }
    }
  });

  console.log(`\n📊 Test Results:`);
  console.log(`   Passed: ${passedTests}`);
  console.log(`   Failed: ${failedTests}`);
  console.log(`   Total: ${testCases.length}`);
  
  if (failedTests === 0) {
    console.log('🎉 All tests passed!');
  } else {
    console.log('⚠️  Some tests failed. Please check the validation logic.');
  }

  return { passedTests, failedTests, total: testCases.length };
};

// Export for use in other files or run immediately
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { testValidation };
} else {
  // Run tests immediately if in browser
  testValidation();
}
