import { Collapse, Input, Select, Tooltip } from "antd";
import {
  ChevronDown,
  ChevronLeft,
  Eye,
  GripVertical,
  Loader2,
  Search,
  X,
} from "lucide-react";
import React, { useEffect, useRef, useState } from "react";
import { generateGlobalPreviewHTML } from "../../Components/content";
import { useDrag } from "react-dnd";
import useHttp from "../../../hooks/use-http";
import { CONSTANTS } from "../../../util/constant/CONSTANTS";
import { apiGenerator } from "../../../util/functions";
import runtimeCache from "../../../utils/runtimeCache";
import Paragraph from "antd/es/typography/Paragraph";
import CustomTooltip from "../../common/CustomTooltip";
import SearchBar from "../../common/SearchBar";

const PageLibrary = ({
  isComponentOpen,
  setIsComponentOpen,
  isMobile,
  isTablet,
  type,
  listData = [],
  categories = [],
  loadComponentsByCategory,
  loadingCategories,
  isLoading = false,
  onSearchChange,
  handleGlobalFieldChange,
  // LibraryItem,
}) => {
  const api = useHttp();
  const [componentPreviews, setComponentPreviews] = useState({});
  const [expandedPreviews, setExpandedPreviews] = useState({});
  // Draggable Component Library item - NO debouncing, immediate drag
  const LibraryItem = React.memo(({ comp }) => {
    const [{ isDragging }, drag] = useDrag(
      () => ({
        type: type,
        item: () => {
          // console.log("Starting library component drag:", comp.name);
          return { component: comp };
        },
        collect: (monitor) => ({ isDragging: monitor.isDragging() }),
        end: (_, monitor) => {
          if (monitor.didDrop()) {
            // console.log("Library component dropped successfully:", comp?.name);
          } else {
            // console.log("Library component drag cancelled:", comp?.name);
          }
        },
      }),
      [comp]
    );

    const componentId = comp?.id;
    const versionId = comp?.version?.id;
    const cacheKey = `${componentId}-${versionId}`;
    // const isExpanded = expandedPreviews[componentId];
    // const isLoading = api.isLoading && componentPreviews[componentId]?.loading;
    // Show loader based on our own preview state, not the global API flag
    const isExpanded = !!expandedPreviews[componentId];
    const isLoadingPreview = !!componentPreviews[componentId]?.loading;

    const handlePreviewExpand = (version) => {
      if ((!isExpanded && !componentPreviews[componentId]) || version) {
        setExpandedPreviews((prev) => ({ ...prev, [componentId]: true }));

        // Check cache first
        const cachedPreview = runtimeCache.getCachedComponent(
          componentId,
          version?.version || comp?.version?.version
        );
        if (cachedPreview && cachedPreview.html) {
          setComponentPreviews((prev) => ({
            ...prev,
            [componentId]: { ...cachedPreview, loading: false },
          }));
          return;
        }

        // Set loading state
        setComponentPreviews((prev) => ({
          ...prev,
          [componentId]: { loading: true },
        }));

        // Fetch from API
        api.sendRequest(
          apiGenerator(CONSTANTS.API.componentVersions.getById, {
            id: version ? version?.id : versionId,
          }),
          (res) => {
            const fullComponentData = res?.data?.[0] || res;
            runtimeCache.cacheComponent(fullComponentData);
            setComponentPreviews((prev) => ({
              ...prev,
              [componentId]: { ...fullComponentData, loading: false },
            }));
          },
          null,
          null,
          (error) => {
            console.error("Error fetching component preview:", error);
            setComponentPreviews((prev) => ({
              ...prev,
              [componentId]: { loading: false, error: true },
            }));
          }
        );
      }
    };

    const previewContent = () => {
      if (isLoadingPreview) {
        return (
          <div className="tw-flex tw-items-center tw-justify-center tw-h-20 tw-bg-white tw-rounded-lg tw-border-2 tw-border-dashed tw-border-gray-300">
            <div className="tw-text-center">
              <div className="loader tw-mx-auto tw-mb-2"></div>
              <p className="tw-text-xs tw-text-gray-500">Loading preview...</p>
            </div>
          </div>
        );
      }

      const previewData = componentPreviews[componentId] || comp;
      const htmlContent =
        previewData?.html || comp?.componentversions?.[0]?.html;

      if (htmlContent) {
        return (
          <div
            className="tw-bg-white tw-rounded-lg tw-p-1 tw-mb-0 tw-max-h-24 tw-relative tw-overflow-hidden"
            style={{ height: "120px" }}
          >
            <iframe
              srcDoc={generateGlobalPreviewHTML({
                data: [{ ...comp, ...previewData }],
              })}
              className="tw-w-full tw-h-full tw-border-0 tw-rounded-lg tw-origin-top-left"
              title="Component Preview"
              style={{
                transform: "scale(0.2)",
                width: "500%",
                height: "500%",
                transformOrigin: "top left",
              }}
            />
          </div>
        );
      }

      return (
        <div className="tw-flex tw-items-center tw-justify-center tw-h-20 tw-bg-white tw-rounded-lg tw-border-2 tw-border-dashed tw-border-gray-300">
          <div className="tw-text-center">
            <Eye className="tw-w-6 tw-h-6 tw-text-gray-400 tw-mx-auto tw-mb-1" />
            <p className="tw-text-xs tw-text-gray-500">No preview available</p>
          </div>
        </div>
      );
    };

    return (
      <div className="tw-bg-gray-50 tw-rounded-lg tw-border tw-border-gray-200 tw-hover:tw-border-gray-300 tw-transition-colors">
        {/* Main component card */}
        <div
          ref={drag}
          className="tw-p-3 tw-pb-0 tw-cursor-move tw-select-none"
          style={{ opacity: isDragging ? 0.6 : 1 }}
        >
          <div className="tw-flex tw-items-center tw-justify-between tw-mb-3">
            <div className="tw-flex-1">
              {/* <p className="tw-text-sm tw-font-medium tw-text-gray-900">
                {comp?.name}
              </p> */}
              <Paragraph
                ellipsis={{ rows: 2, expandable: false, tooltip: true }}
                className="tw-text-gray-900 tw-text-sm !tw-mb-0"
              >
                {comp?.name}
              </Paragraph>
            </div>
            <GripVertical className="tw-w-4 tw-h-4 tw-text-gray-400" />
          </div>
          <div>
            <Select
              size="large"
              placeholder="V1"
              value={comp?.version?.id}
              onChange={(value, option) => {
                handleGlobalFieldChange(componentId, "version", option?.extra);
                if (expandedPreviews[componentId]) {
                  handlePreviewExpand(option?.extra);
                }
              }}
              onMouseDown={(e) => e.stopPropagation()}
              onFocus={(e) => e.stopPropagation()}
              className="tw-w-12 tw-h-3 tw-text-sm version-class"
              styles={{
                optionFontSize: "5px",
              }}
              style={{
                width: 54, // Adjust the width as needed
                height: 30,
                borderRadius: "100px", // Creates the pill shape
                fontSize: "12px",
              }}
              options={
                comp?.componentversions?.map((v) => ({
                  value: v?.id,
                  label: `v${v?.version}`,
                  extra: {
                    id: v?.id,
                    version: v?.version,
                  },
                })) || []
                //   [
                //   { value: +"1", label: "v1" },
                //   { value: +"2", label: "v2" },
                //   { value: +"3", label: "v3" },
                // ]
              }
            />
          </div>
        </div>

        {/* Ant Design Collapse for Preview */}
        <Collapse
          ghost
          size="small"
          className="tw-bg-transparent"
          expandIcon={({ isActive }) => (
            <ChevronDown
              className={`tw-w-3 tw-h-3 tw-text-gray-400 tw-transition-transform tw-duration-200 ${
                isActive ? "tw-rotate-180" : ""
              }`}
            />
          )}
          expandIconPosition="end"
          activeKey={isExpanded ? ["preview"] : []}
          onChange={(activeKeys) => {
            const nowOpen =
              Array.isArray(activeKeys) && activeKeys.includes("preview");
            setExpandedPreviews((prev) => ({
              ...prev,
              [componentId]: nowOpen,
            }));
            if (nowOpen) handlePreviewExpand();
            // if (activeKeys?.includes("preview")) {
            //   handlePreviewExpand();
            // }
          }}
          items={[
            {
              key: "preview",
              label: (
                <span className="tw-text-xs tw-text-gray-600">Preview</span>
              ),
              children: <div className="tw-p-0">{previewContent()}</div>,
            },
          ]}
        />
      </div>
    );
  });
  return (
    <>
      <div
        className={`${
          isComponentOpen
            ? isMobile
              ? "tw-fixed tw-inset-0 tw-z-50 tw-w-full"
              : isTablet
              ? "tw-w-64"
              : "tw-w-[18rem]"
            : "tw-w-0 tw-overflow-hidden"
        } tw-bg-white tw-border-r tw-border-gray-200 tw-flex tw-flex-col tw-transition-all tw-duration-300 tw-ease-in-out ${
          isMobile && isComponentOpen ? "tw-shadow-2xl" : ""
        }`}
      >
        <div className="tw-border-b tw-border-gray-200 tw-flex tw-items-center tw-justify-between">
          <div className="tw-flex tw-h-full tw-items-center tw-justify-center tw-border-r tw-border-gray-200">
            <Tooltip
              title={
                isComponentOpen ? "Hide Component List" : "Show Component List"
              }
            >
              <button
                onClick={() => setIsComponentOpen((v) => !v)}
                className="tw-text-gray-600 tw-px-3 tw-flex tw-items-center tw-justify-center tw-rounded-lg"
              >
                <ChevronLeft
                  size={30}
                  className={` ${isComponentOpen ? "" : "tw-rotate-180 "}`}
                />
              </button>
            </Tooltip>
          </div>
          <div className="tw-p-3 md:tw-p-4 tw-border-gray-200">
            <div className="tw-flex tw-items-center tw-justify-between">
              <div>
                <h3 className="tw-text-base md:tw-text-lg tw-font-semibold tw-text-gray-900 tw-mb-1">
                  Component Library
                </h3>
                <p className="tw-text-xs tw-text-gray-600 tw-hidden md:tw-block">
                  Drag components to build your page.
                </p>
              </div>
              {isMobile && (
                <button
                  onClick={() => setIsComponentOpen(false)}
                  className="tw-p-2 tw-text-gray-400 tw-hover:tw-text-gray-600 tw-rounded-lg"
                >
                  <X className="tw-w-5 tw-h-5" />
                </button>
              )}
            </div>
          </div>
        </div>

        <div className="tw-flex-1 tw-overflow-y-auto tw-p-3 md:tw-p-4">
          <SearchBar
            placeholder="Search..."
            type="page"
            handleSearch={(e) => onSearchChange(e)}
          />
          {/* <Input
            placeholder="Search components"
            size="middle"
            prefix={
              isLoading ? (
                <Loader2 className="tw-w-4 tw-h-4 tw-text-blue-500 tw-animate-spin" />
              ) : (
                <Search className="tw-w-4 tw-h-8 tw-text-gray-400" />
              )
            }
            // value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="tw-rounded-lg tw-mb-2"
            allowClear
          /> */}
          <Collapse
            ghost
            size="small"
            className="tw-bg-transparent tw-mt-2"
            expandIcon={({ isActive }) => (
              <ChevronDown
                className={`tw-w-4 tw-h-4 tw-text-gray-400 tw-transition-transform tw-duration-200 ${
                  isActive ? "tw-rotate-180" : ""
                }`}
              />
            )}
            expandIconPosition="end"
            onChange={(activeKeys) => {
              // Handle category expansion - load components when category is expanded
              if (activeKeys && activeKeys.length > 0) {
                const expandedKey = activeKeys[activeKeys.length - 1]; // Get the most recently expanded key
                if (
                  loadComponentsByCategory &&
                  !loadingCategories?.has(expandedKey)
                ) {
                  loadComponentsByCategory(expandedKey);
                }
              }
            }}
            items={categories.map((category) => {
              const categoryComponents =
                listData[category.id]?.components || [];
              const isLoading = loadingCategories?.has(category.id);
              const hasComponents = categoryComponents.length > 0;
              const hasLoadedComponents = hasComponents && !isLoading;

              return {
                key: category?.id,
                label: (
                  <div className="tw-flex tw-items-center tw-text-center">
                    <div className="tw-flex tw-items-center  tw-gap-x-2 tw-min-w-0">
                      <div
                        className="tw-w-4 tw-h-4 tw-rounded-full tw-flex-shrink-0"
                        style={{
                          backgroundColor: category?.colour || "#3B82F6",
                        }}
                      />
                      <Paragraph
                        ellipsis={{ rows: 1, expandable: false, tooltip: true }}
                        level={4}
                        className="!tw-mb-0 tw-text-gray-900 tw-font-semibold tw-truncate tw-text-start tw-min-w-0 tw-max-w-[100px]"
                      >
                        {category?.name}
                      </Paragraph>
                    </div>

                    <span className="tw-ml-2 tw-text-sm tw-text-gray-500">
                      {category?.componentCount || hasLoadedComponents
                        ? `(${
                            category?.componentCount ||
                            categoryComponents?.length ||
                            0
                          })`
                        : ""}
                    </span>
                    {/* {isLoading && (
                      <Loader2 className="tw-w-3 tw-h-3 tw-text-blue-500 tw-animate-spin tw-ml-1" />
                    )} */}
                  </div>
                ),
                children: (
                  <div className="tw-space-y-2 tw-ml-2">
                    {isLoading ? (
                      <div className="tw-text-center tw-py-4">
                        <Loader2 className="tw-w-4 tw-h-4 tw-text-blue-500 tw-animate-spin tw-mx-auto" />
                        <p className="tw-text-sm tw-text-gray-500 tw-mt-2">
                          Loading components...
                        </p>
                      </div>
                    ) : hasComponents ? (
                      categoryComponents?.map((component) => (
                        <LibraryItem key={component?.id} comp={component} />
                      ))
                    ) : (
                      <div className="tw-text-center tw-py-4">
                        <p className="tw-text-sm tw-text-gray-500">
                          No components in this category
                        </p>
                      </div>
                    )}
                  </div>
                ),
              };
            })}
          />

          {categories.length === 0 && (
            <div className="tw-text-center tw-py-8">
              <p className="tw-text-gray-500">
                No categories and components available
              </p>
              <p className="tw-text-sm tw-text-gray-400 tw-mt-1">
                Create categories and components first to use them here
              </p>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default PageLibrary;
