import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { Spin, Empty } from "antd";

/**
 * Reusable ScrollPagination component (infinite scroll)
 *
 * Props:
 * - loadPage: ({ page, pageSize }) => Promise<{ items: any[], totalCount: number }>
 * - renderItem: (item, index) => React.ReactNode
 * - pageSize?: number (default 12)
 * - initialPage?: number (default 1)
 * - useWindow?: boolean (default true) - whether to use window scroll instead of container
 * - className?: string - wrapper class for items grid/list
 * - emptyDescription?: string
 */
const ScrollPagination = ({
  loadPage,
  renderItem,
  itemList,
  // type="static",
  pageSize = 12,
  initialPage = 0,
  useWindow = true,
  className = "",
  emptyScreen,
  emptyDescription = "No data found",
  updatedItem,
  idKey = "id",
  refreshKey = null,
  filterIds = [],
}) => {
  const [items, setItems] = useState(itemList || []);
  const [page, setPage] = useState(initialPage);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);

  const sentinelRef = useRef(null);
  const containerRef = useRef(null);
  // Simplified loading state management
  const isLoadingRef = useRef(false);

  useEffect(() => {
    if (!itemList) return;
    setItems(itemList);
  }, [itemList]);

  const load = useCallback(
    async (nextPage) => {
      if (loading || isLoadingRef.current) return;
      setLoading(true);
      isLoadingRef.current = true;
      try {
        const { items: newItems, totalCount } = await loadPage({
          page: nextPage,
          pageSize,
        });
        if (!itemList) {
          setItems((prev) =>
            nextPage === initialPage ? newItems : [...prev, ...newItems]
          );
        }
        setTotal(totalCount || 0);
        const loadedCount = (nextPage - 1) * pageSize + (newItems?.length || 0);
        setHasMore(loadedCount < (totalCount || 0));
        setPage(nextPage);
      } catch (e) {
        // On error, stop further loading to avoid loops
        setHasMore(false);
        console.error("ScrollPagination load error:", e);
      } finally {
        setLoading(false);
        // Add delay before allowing next load
        setTimeout(() => {
          isLoadingRef.current = false;
        }, 500);
      }
    },
    [
      loadPage,
      pageSize,
      initialPage,
      loading,
      refreshKey,
      items.length,
      itemList,
    ]
  );

  // Initial load and refresh handling
  useEffect(() => {
    if (refreshKey == null) return;
    load(initialPage);
  }, [refreshKey]);

  // IntersectionObserver to detect when the sentinel is visible
  useEffect(() => {
    const target = sentinelRef.current;
    if (!target) return;

    const root = useWindow ? null : containerRef.current;

    const observer = new IntersectionObserver(
      (entries) => {
        const first = entries[0];

        if (
          first.isIntersecting &&
          hasMore &&
          !loading &&
          !isLoadingRef.current
        ) {
          load(page + 1);
        }
      },
      {
        root,
        rootMargin: "0px 0px 200px 0px", // Reduced margin for more predictable behavior
        threshold: 0.1,
      }
    );

    observer.observe(target);
    return () => observer.disconnect();
  }, [hasMore, loading, load, page, useWindow]);

  // Patch a single updated item (or create) when provided by parent
  useEffect(() => {
    if (!updatedItem) return;
    setItems((prev) => {
      if (updatedItem.type === "create") {
        return [updatedItem, ...(prev || [])];
      }
      if (updatedItem.type === "delete") {
        return (prev || []).filter(
          (it) => it?.[idKey] !== updatedItem?.[idKey]
        );
      }
      return (prev || []).map((it) =>
        it?.[idKey] === updatedItem?.[idKey] ? { ...it, ...updatedItem } : it
      );
    });
  }, [updatedItem, idKey]);

  const content = useMemo(() => {
    if (filterIds.length > 0) {
      return items?.map((item, idx) => {
        if (filterIds.includes(item?.[idKey])) {
          return null;
        }
        return renderItem(item, idx);
      });
    }
    return items?.map((item, idx) => renderItem(item, idx));
  }, [items, loading, renderItem, emptyScreen, emptyDescription]);

  return (
    <div ref={containerRef} className="tw-w-full">
      {!items?.length && !loading ? (
        <div className="tw-py-12 tw-w-full tw-flex tw-items-center tw-justify-center">
          {emptyScreen}
          {/* <Empty description={emptyDescription} /> */}
        </div>
      ) : (
        <div className={className}>{content}</div>
      )}

      {/* Sentinel and loader with improved spacing */}
      <div className="tw-mb-6">
        <div
          ref={sentinelRef}
          className="tw-h-4 tw-w-full"
          style={{ backgroundColor: "transparent" }}
        />
        {loading && (
          <div className="tw-flex tw-h-full tw-justify-center tw-items-center tw-p-4">
            <Spin />
          </div>
        )}
        {!hasMore && items?.length > 0 && !loading && (
          <div className="tw-text-center tw-text-gray-400 tw-text-xs tw-py-3">
            No more results
          </div>
        )}
      </div>
    </div>
  );
};

export default ScrollPagination;
