import React from "react";
import { <PERSON>, Avatar, Tag, Space, <PERSON><PERSON>ip, <PERSON><PERSON><PERSON>rm, <PERSON><PERSON> } from "antd";
import { <PERSON>, <PERSON>cil, Trash2, User, Users } from "lucide-react";
import { ShieldUser } from "../../util/constant/ICON";
import Paragraph from "antd/es/typography/Paragraph";
// Date formatting utility
const formatDate = (date) => {
  if (!date) return "N/A";
  const d = new Date(date);
  return d.toLocaleDateString("en-GB"); // DD/MM/YYYY format
};

const UserTable = ({
  users,
  loading,
  deleteLoading = {},
  onEdit,
  onView,
  onDelete,
}) => {
  const getRoleIcon = (role) => {
    return role === "admin" ? (
      <ShieldUser className="tw-w-4 tw-h-4" />
    ) : (
      <Users className="tw-w-4 tw-h-4" />
    );
  };

  const getRoleColor = (role) => {
    switch (role) {
      case "admin":
        return "blue";
      case "content-team":
        return "purple";
      default:
        return "default";
    }
  };

  const getRoleText = (role) => {
    switch (role) {
      case "admin":
        return "Admin";
      case "content-team":
        return "Content Team";
      default:
        return role;
    }
  };

  const columns = [
    {
      title: "User",
      dataIndex: "name",
      key: "name",
      width: 180,
      render: (text, record) => (
        <Space>
          <Avatar
            size={40}
            style={{
              backgroundColor: "#374151",
            }}
          >
            {!!text?.trim() ? (
              <p>{text?.charAt(0)?.toUpperCase()}</p>
            ) : (
              <User className="tw-w-6 tw-h-6" />
            )}
          </Avatar>
          <div className="tw-flex tw-flex-col tw-truncate">
            <Paragraph
              ellipsis={{ rows: 1, expandable: false, tooltip: true }}
              className="!tw-mb-0 tw-text-gray-900 tw-text-sm tw-truncate tw-max-w-48"
            >
              {text}
            </Paragraph>
            {/* <div  style={{ fontWeight: 500, fontSize: "14px" }}>{text}</div> */}
            <div style={{ color: "#8c8c8c", fontSize: "12px" }}>
              {record.email}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: "Role",
      dataIndex: "role",
      key: "role",
      width: 80,
      render: (role) => {
        // const RoleIcon = getRoleIcon(role);
        return (
          <Space>
            {/* <RoleIcon className="tw-w-4 tw-h-4" style={{ color: "#8c8c8c" }} /> */}
            <Tag
              className="tw-flex tw-items-center tw-text-center tw-gap-2 tw-space-y-2 tw-rounded-2xl tw-h-[27px]"
              // icon={<ShieldUser className="tw-w-4 tw-h-4" />}
              color={getRoleColor(role)}
            >
              {getRoleIcon(role)}
              {/* <ShieldUser className="tw-w-4 tw-h-4" /> */}
              <p className="tw-text-sm tw-font-normal !tw-m-0">
                {getRoleText(role)}
              </p>
            </Tag>
          </Space>
        );
      },
    },
    {
      title: "Last Login",
      dataIndex: "lastLoginAt",
      key: "lastLoginAt",
      width: 100,
      render: (date) => <span>{date ? formatDate(date) : "Never"}</span>,
    },
    // {
    //   title: "Created",
    //   dataIndex: "created_at",
    //   key: "created_at",
    //   render: (date) => (
    //     <span style={{ color: "#8c8c8c" }}>{formatDate(date)}</span>
    //   ),
    // },
    {
      title: "Actions",
      key: "actions",
      align: "left",
      width: 40,
      render: (_, record) => (
        <div className="tw-gap-0 tw-flex tw-items-center ">
          <Tooltip title="View User">
            <Button
              type="text"
              className="tw-text-gray-500 hover:tw-text-gray-600"
              icon={<Eye className="tw-w-4 tw-h-4 tw-text-gray-500" />}
              onClick={() => onView(record)}
            />
          </Tooltip>
          <Tooltip title="Edit User">
            <Button
              type="text"
              className="tw-text-gray-500 hover:tw-text-gray-600"
              icon={<Pencil className="tw-w-4 tw-h-4 tw-text-gray-500" />}
              onClick={() => onEdit(record)}
            />
          </Tooltip>
          {record.role !== "admin" && (
            <Popconfirm
              title="Are you sure you want to delete this user?"
              description="This action cannot be undone."
              onConfirm={() => onDelete(record?.id)}
              okText="Yes"
              cancelText="No"
              disabled={deleteLoading?.[record?.id]}
            >
              <Tooltip title="Delete User">
                <Button
                  type="text"
                  danger
                  loading={deleteLoading?.[record?.id]}
                  icon={<Trash2 className="tw-w-4 tw-h-4 tw-text-gray-500" />}
                />
              </Tooltip>
            </Popconfirm>
          )}
        </div>
      ),
    },
  ];

  return (
    <div className="custom-table">
      <Table
        columns={columns}
        dataSource={users}
        loading={loading}
        // className="tw-border tw-border-solid  tw-border-gray-300 "
        style={{ borderRadius: "10px" }}
        rowKey="id"
        pagination={{
          pageSize: 10,
          // showSizeChanger: true,
          // showQuickJumper: true,
          showTotal: (total, range) =>
            `${range[0]}-${range[1]} of ${total} users`,
        }}
        // scroll={{ x: 800 }}
      />
    </div>
  );
};

export default UserTable;
