import React, { useState, useEffect } from "react";
import Header from "../Layout/Header";
import { Save, X, Plus, Trash2, FileText, GripVertical } from "lucide-react";
import useHttp from "../../hooks/use-http";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { apiGenerator, imageObj, removeSpacesDeep } from "../../util/functions";
import TemplateTabList from "./Component/TemplateTabList";
import { message } from "antd";
import { useNavigate } from "react-router-dom";

const TemplateEditor = React.memo(
  ({ template, pages, onCancel, setRefresh }) => {
    const api = useHttp();
    const navigate = useNavigate();
    const [formData, setFormData] = useState({
      name: "", // Template name
      description: "",
      full_template_content: "", // New field: template preview full code
      // templateComponentList: [], // New field: template preview page list
      pageData: [], // Existing field for backward compatibility
      // content: [], // New field: array of json
    });
    const [components, setComponents] = useState([]); // Add components state
    const [saving, setSaving] = useState(false);
    // console.log(template, "template", formData, pages);

    useEffect(() => {
      // Fetch components for repeated component functionality
      api.sendRequest(CONSTANTS.API.components.get, (res) => {
        // console.log("Components fetched for template editor:", res);
        setComponents(res);
      });

      if (template) {
        // console.log(template, "template");
        const mediaObj = template?.latestVersion?.mediaSet?.medias;
        setFormData(() => ({
          ...template,
          name: template?.name || "",
          description: template?.description || "",
          full_template_content: template?.full_template_content || "", // New field
          // templateComponentList: template.templateComponentList || [], // New field
          pageData:
            template?.latestVersion?.pageData?.map((pd) => ({
              ...pd,
              id: pd?.pageId,
              ...pd,
              versionData: {
                ...pd,
                urlSlug: pd?.url,
              },
            })) || [],
          content: template?.latestVersion?.content?.content || {}, // New field
          mediaSet: {
            // ...template?.latestVersion?.mediaSet,
            ...(mediaObj ? imageObj(mediaObj || {}) : {}),
          },
        }));
      }
      // else {
      //   // Initialize with empty data for new templates
      //   setFormData(() => ({
      //     name: "",
      //     description: "",
      //     full_template_content: "",
      //     // templateComponentList: [],
      //     pageData: [], // {"url":"/","showNavbar":true,"type":"static","pageVersionId":2,"name":"home","navbarIndex":0}
      //     content: {},
      //   }));
      // }

      // setAvailablePages(pages || []);
    }, [template?.id]); // Only depend on template.id to avoid unnecessary re-renders

    const handleSubmit = async (submittedFormData, extraCall) => {
      // setSaving(true);
      // console.log(submittedFormData, "final submit");

      try {
        // Step 1: Save template data (create or update)
        const templateApiConfig = template
          ? apiGenerator(
              CONSTANTS.API.templates.update,
              {
                id: template?.id,
                // ...(submittedFormData?.mediaChanged ? { mediaChanged: true } : {}),
              },
              submittedFormData?.mediaChanged
                ? `?mediaChanged=${submittedFormData?.mediaChanged}`
                : null
            )
          : CONSTANTS.API.templates.create;

        // Prepare template data (exclude media-specific fields)
        // const templateData = {
        //   name: submittedFormData.name,
        //   description: submittedFormData.description,
        //   full_template_content: submittedFormData.full_template_content,
        //   pages: submittedFormData.pages,
        //   contentJSON: submittedFormData.contentJSON,
        //   templateContentJSON: submittedFormData.templateContentJSON,
        // };

        const finalPayload = removeSpacesDeep({
          name: submittedFormData?.name,
          description: submittedFormData?.description,
          pageData: submittedFormData?.pageData?.map((pd) => {
            return {
              ...(pd?.showNavbar ? { navbarIndex: pd?.navbarIndex } : {}),
              name: pd?.name,
              pageVersionId: pd?.pageVersionId,

              pageId: pd?.pageId,
              pageVersion:
                pd?.versionData?.version || pd?.versionData?.pageVersion,
              showNavbar: pd?.showNavbar,
              type: pd?.type,
              url: pd?.urlSlug,
              siteMapLabel: pd?.siteMapLabel,
              wrapperClass: pd?.wrapperClass,
              customCss: pd?.customCss,
              customJs: pd?.customJs,
              headers: pd?.headers,
              metaTitle: pd?.metaTitle,
              metaDescription: pd?.metaDescription,
            };
          }),
          content: submittedFormData?.contentJSON,
        });
        // For update: send only changed keys
        const templatePayload = template
          ? Object.keys(finalPayload)?.reduce((acc, key) => {
              if (finalPayload[key] !== template[key])
                acc[key] = finalPayload[key];
              return acc;
            }, {})
          : finalPayload;
        // console.log(finalPayload, "finalPayload...");
        // const isTemplateRedirect =
        // Save template first
        api.sendRequest(
          templateApiConfig,
          async (templateResponse) => {
            // console.log("Template saved successfully:", templateResponse);

            // Get template ID from response
            const templateId =
              templateResponse.data?.template?.id ||
              templateResponse.templateVersion?.templateId ||
              template?.id;
            const templateVersionData =
              templateResponse.data?.templateVersion ||
              templateResponse.templateVersion;
            const templateVersionId =
              templateVersionData?.id || templateVersionData?.templateVersionId;

            const extraPayload = {
              id: templateId,
              templateId: templateId,
              templateVersionId: templateVersionId,
              templateVersion: templateVersionData?.version,
              templateContentId: templateVersionData?.contentId,
            };

            // Step 2: Handle media upload if there are new files or removals
            if (
              templateId &&
              (submittedFormData?.newMediaFiles?.length > 0 ||
                submittedFormData?.removeMediaIds?.length > 0)
            ) {
              await handleMediaUpload(
                templateId,
                templateVersionId,
                submittedFormData,
                extraPayload
              );
            } else if (!template) {
              await handleMediaUpload(
                templateId,
                templateVersionId,
                submittedFormData,
                extraPayload
              );
            } else if (!submittedFormData?.mediaChanged) {
              // Update formData with response
              setFormData((prevData) => ({
                ...prevData,
                // ...(templateResponse?.data || templateResponse),
                id: templateId,
                templateId: templateId,
                templateVersionId: templateVersionId,
                templateVersion: templateVersionData?.version,
                templateContentId: templateVersionData?.contentId,
              }));
            }

            // Step 3: Execute callback and finish
            if (extraCall) {
              extraCall(templateResponse);
            }

            setSaving(false);
            message.success(
              template
                ? "Template updated successfully!"
                : "Template created successfully!"
            );
          },
          templatePayload,
          null, // No automatic success message - we'll handle it manually
          (error) => {
            console.error("Error saving template:", error);
            message.error(
              error || "Failed to save template. Please try again."
            );
            setSaving(false);
          }
        );
      } catch (error) {
        console.error("Error in handleSubmit:", error);
        message.error("An unexpected error occurred. Please try again.");
        setSaving(false);
      }
    };

    // Handle media upload/removal
    const handleMediaUpload = async (
      templateId,
      templateVersionId,
      submittedFormData,
      extraPayload
    ) => {
      try {
        // Prepare FormData for media upload
        const convertFormData = new FormData();
        if (!submittedFormData?.mediaSet) {
          if (!template) {
            navigate(`/templates/${templateId}`);
          }
          return;
        }

        // Add new media files
        if (
          template?.id &&
          Object.values(submittedFormData?.newMediaFiles)?.length > 0
        ) {
          Object.values(submittedFormData?.newMediaFiles).forEach(
            (mediaFile) => {
              convertFormData.append("media", mediaFile?.file);
            }
          );
        }

        // Add removeMediaIds as comma-separated string
        if (template?.id && submittedFormData?.removeMediaIds?.length > 0) {
          convertFormData.append(
            "removeMediaIds",
            submittedFormData.removeMediaIds.join(",")
          );
        }

        if (
          !template?.id &&
          submittedFormData?.mediaSet &&
          Object.values(submittedFormData?.mediaSet)?.length > 0
        ) {
          Object.values(submittedFormData?.newMediaFiles).forEach(
            (mediaFile) => {
              convertFormData.append("media", mediaFile.file);
            }
          );
        }

        // Call media API
        // const mediaApiUrl = `${CONSTANTS.API.templates.addMedia.endpoint}?id=${templateId}&type=template`;
        // const mediaApiConfig = {
        //   ...CONSTANTS.API.templates.addMedia,
        //   endpoint: mediaApiUrl,
        // };

        api.sendRequest(
          apiGenerator(
            CONSTANTS.API.templates.addMedia,
            {},
            `?id=${templateVersionId}&type=template`
          ),
          async (mediaResponse) => {
            // console.log("Media uploaded successfully:", mediaResponse);
            if (!template) {
              setFormData((prevData) => ({
                ...prevData,
                mediaChanged: false,
                ...(extraPayload ? extraPayload : {}),
              }));
              navigate(`/templates/${templateId}`);
            } else {
              // Fetch updated media list from server after successful upload
              try {
                api.sendRequest(
                  apiGenerator(CONSTANTS.API.templates.getById, {
                    id: templateId,
                  }),
                  (templateData) => {
                    // console.log("Updated template data:", templateData);
                    const updatedMediaSet =
                      templateData?.data?.latestVersion?.mediaSet?.medias || [];

                    setFormData((prevData) => ({
                      ...prevData,
                      mediaChanged: false,
                      removeMediaIds: [],
                      newMediaFiles: [],
                      mediaSet: updatedMediaSet, // Update with fresh server data
                      ...(extraPayload ? extraPayload : {}),
                    }));

                    // Force re-render of child components to sync state
                    setTimeout(() => {
                      setFormData((prevData) => ({
                        ...prevData,
                        mediaResetTrigger: Date.now(), // Add trigger to force child components to reset
                      }));
                    }, 100);
                  }
                );
              } catch (error) {
                console.error("Error fetching updated media:", error);
                // Fallback to original behavior if refresh fails
                setFormData((prevData) => ({
                  ...prevData,
                  mediaChanged: false,
                  removeMediaIds: [],
                  newMediaFiles: [],
                  ...(extraPayload ? extraPayload : {}),
                }));

                setTimeout(() => {
                  setFormData((prevData) => ({
                    ...prevData,
                    mediaResetTrigger: Date.now(),
                  }));
                }, 100);
              }
            }
            // message.success("Media files processed successfully!");
          },
          convertFormData,
          null, // No automatic success message
          (error) => {
            console.error("Error uploading media:", error);
            message.error(
              "Failed to process media files. Template saved but media upload failed."
            );
          }
        );
      } catch (error) {
        console.error("Error in handleMediaUpload:", error);
        message.error("Failed to process media files.");
      }
    };

    return (
      <div className="tw-h-screen tw-flex tw-flex-col tw-overflow-hidden">
        <TemplateTabList
          pages={pages}
          components={components}
          formData={formData}
          setFormData={setFormData}
          template={template}
          onCancel={onCancel}
          handleSubmit={handleSubmit}
          saving={saving}
        />
      </div>
    );
  }
);

export default TemplateEditor;
