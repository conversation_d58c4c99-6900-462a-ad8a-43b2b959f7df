import { Input, Select, Tooltip } from "antd";
import {
  ChevronLeft,
  FileText,
  GripVertical,
  Loader2,
  Search,
} from "lucide-react";
import React, { useEffect, useMemo, useState, useCallback } from "react";
import { useDrag } from "react-dnd";
import SearchBar from "../../common/SearchBar";
import ScrollPagination from "../../common/ScrollPagination";
import useHttp from "../../../hooks/use-http";
import { CONSTANTS } from "../../../util/constant/CONSTANTS";
import useDebounce from "../../../hooks/useDebounce";
import runtimeCache from "../../../utils/runtimeCache";
import Paragraph from "antd/es/typography/Paragraph";

const TemplateLibrary = ({
  isPageLibraryOpen,
  setIsPageLibraryOpen,
  screenSize,
  availablePages,
  onAddPageToTemplate, // New prop for handling page drops
  usedPageIds = [], // Array of page IDs already used in template
  templateId = "current", // Current template ID for caching
  onPageDragStart, // Callback when page drag starts
  onPageDragEnd, // Callback when page drag ends
  setReloadTrigger,
  reloadTrigger,
}) => {
  const api = useHttp();
  const [searchTerm, setSearchTerm] = useState("");
  // const [reloadTrigger, setReloadTrigger] = useState(0);
  const [itemList, setItemList] = useState([]);
  // Debounced search term
  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  // Trigger reload when usedPageIds changes
  // useEffect(() => {
  //   console.log("djhgjfnhgkifg", usedPageIds);
  //   setReloadTrigger((prev) => prev + 1);
  // }, [usedPageIds]);

  // Load pages function for ScrollPagination with caching
  const loadPages = useCallback(
    async ({ page, pageSize }) => {
      try {
        // Check cache first for available pages (filtered by used pages)
        const cachedAvailablePages =
          runtimeCache.getCachedAvailableTemplatePages(templateId, usedPageIds);
        if (cachedAvailablePages && !debouncedSearchTerm && page == 1) {
          setItemList(cachedAvailablePages);
          return {
            items: cachedAvailablePages.slice(0, pageSize),
            totalCount: cachedAvailablePages.length,
          };
        }

        // Check cache for all template pages
        let allPages = runtimeCache.getCachedTemplatePages(templateId);

        if (!allPages) {
          // Fetch from API if not cached
          const searchParams = {
            page: 1, // Always fetch all pages for caching
            limit: 1000, // Large limit to get all pages
            ...(debouncedSearchTerm && { search: debouncedSearchTerm }),
          };

          const response = await new Promise((resolve, reject) => {
            api.sendRequest(
              CONSTANTS.API.pages.getAllForTemplate,
              (response) => {
                const pages = response.data?.rows || [];
                const totalCount = response.data?.count || 0;

                // Map with version data
                const processedPages =
                  pages?.map((page) => ({
                    ...page,
                    versionData: page?.pageversions?.length
                      ? page?.pageversions?.reduce((acc, v) => {
                          return acc?.id > v?.id ? acc : v;
                        }) || []
                      : [],
                    type: "static",
                  })) || [];
                setItemList(processedPages);
                resolve({
                  items: processedPages,
                  totalCount: totalCount,
                });
              },
              searchParams,
              null, // No success message
              (error) => {
                console.error("Error loading pages:", error);
                reject(error);
              }
            );
          });

          allPages = response.items;

          // Cache all pages if no search term
          if (!debouncedSearchTerm) {
            runtimeCache.cacheTemplatePages(allPages, templateId);
          }
        }

        // Filter out used pages
        const availablePages = allPages.filter(
          (page) => !usedPageIds.includes(page.id)
        );
        // Apply search filter if needed
        const searchFilteredPages = debouncedSearchTerm
          ? availablePages.filter(
              (page) =>
                page.name
                  ?.toLowerCase()
                  .includes(debouncedSearchTerm.toLowerCase()) ||
                page.slug
                  ?.toLowerCase()
                  .includes(debouncedSearchTerm.toLowerCase())
            )
          : availablePages;

        // Cache available pages if no search term
        if (!debouncedSearchTerm) {
          runtimeCache.cacheAvailableTemplatePages(
            availablePages,
            templateId,
            usedPageIds
          );
        }

        // Apply pagination
        const startIndex = page * pageSize;
        const endIndex = startIndex + pageSize;
        const paginatedPages = searchFilteredPages?.slice(startIndex, endIndex);

        setItemList(paginatedPages);
        return {
          items: paginatedPages,
          totalCount: searchFilteredPages.length,
        };
      } catch (error) {
        console.error("Error in loadPages:", error);
        throw error;
      }
    },
    [api, debouncedSearchTerm, templateId, usedPageIds, reloadTrigger]
  );

  // Draggable Page Item Component
  const PageLibraryItem = ({ page }) => {
    const [{ isDragging }, drag] = useDrag(
      () => ({
        type: "PAGE_ITEM",
        item: () => {
          // console.log("Starting page drag:", page, page.name);
          // Notify parent component about drag start
          if (onPageDragStart) {
            onPageDragStart(page);
          }
          return { page };
        },
        collect: (monitor) => ({ isDragging: monitor.isDragging() }),
        end: (_, monitor) => {
          if (monitor.didDrop()) {
            // console.log("Page dropped successfully:", page.name);
            // Update cache to reflect page is no longer available
            runtimeCache.updateTemplatePageAvailability(
              page.id,
              false,
              templateId
            );
          } else {
            // console.log("Page drag cancelled:", page.name);
          }
          // Notify parent component about drag end
          if (onPageDragEnd) {
            onPageDragEnd(page, monitor.didDrop());
          }
        },
      }),
      [page, onPageDragStart, onPageDragEnd, templateId]
    );

    return (
      <div
        ref={drag}
        className="tw-bg-white tw-rounded-lg  tw-border tw-border-gray-200 tw-hover:tw-border-gray-300 tw-transition-colors tw-p-3 tw-cursor-move tw-select-none"
        style={{ opacity: isDragging ? 0.6 : 1 }}
      >
        <div className="tw-flex tw-items-center tw-justify-between tw-mb-2">
          <div className="tw-flex-1 tw-flex tw-flex-col tw-truncate tw-gap-3">
            {/* <p className="tw-text-sm tw-font-medium tw-text-gray-900">
              {page?.name}
            </p> */}
            <Paragraph
              ellipsis={{ rows: 1, expandable: false, tooltip: true }}
              className="tw-text-gray-900 tw-text-sm !tw-mb-0 tw-max-w-48"
            >
              {page?.name}
            </Paragraph>
            <Select
              size="large"
              placeholder="V1"
              value={page?.versionData?.id}
              onChange={(value) => {
                // Update the page's version data locally
                page.versionData =
                  page?.pageversions?.find((v) => v.id === value) ||
                  page.versionData;
              }}
              onMouseDown={(e) => e.stopPropagation()}
              onFocus={(e) => e.stopPropagation()}
              className="tw-w-12 tw-h-3 tw-text-sm version-class"
              styles={{
                optionFontSize: "5px",
              }}
              style={{
                width: 54, // Adjust the width as needed
                height: 30,
                borderRadius: "100px", // Creates the pill shape
                fontSize: "12px",
              }}
              options={page?.pageversions?.map((ver) => ({
                label: `v${ver?.version}`,
                value: ver?.id,
              }))}
            />
            {/* <p className="tw-text-xs tw-text-gray-500">/{page.slug}</p> */}
          </div>
          <GripVertical className="tw-w-4 tw-h-4 tw-text-gray-400" />
        </div>
      </div>
    );
  };

  return (
    <>
      {/* Left Sidebar - Page Library */}
      <div
        className={`tw-bg-white tw-border-r tw-border-gray-200 tw-flex tw-flex-col tw-transition-all tw-duration-300 tw-ease-in-out ${
          isPageLibraryOpen
            ? screenSize?.isMobile
              ? "tw-fixed tw-inset-0 tw-z-50 tw-w-full"
              : screenSize?.isTablet
              ? "tw-w-64"
              : "tw-w-80"
            : "tw-w-0 tw-overflow-hidden"
        } ${screenSize?.isMobile && isPageLibraryOpen ? "tw-shadow-2xl" : ""}`}
      >
        {isPageLibraryOpen && (
          <>
            <div className="tw-border-b tw-border-gray-200 tw-flex tw-items-center tw-justify-between">
              {!screenSize?.isMobile && (
                <div className="tw-flex tw-h-full tw-items-center tw-justify-center tw-border-r tw-border-gray-200">
                  <Tooltip title="Hide Page Library">
                    <button
                      onClick={() => setIsPageLibraryOpen(false)}
                      className="tw-text-gray-600 tw-px-3 tw-flex tw-items-center tw-justify-center tw-rounded-lg"
                    >
                      <ChevronLeft size={30} />
                    </button>
                  </Tooltip>
                </div>
              )}
              <div className="tw-p-3 md:tw-p-4">
                <h3 className="tw-text-lg tw-font-semibold tw-text-gray-900">
                  Page Library
                </h3>
                <p className="tw-text-sm tw-text-gray-500">
                  Drag pages to build your Template
                </p>
              </div>
            </div>

            <div className="tw-flex-1 tw-overflow-hidden tw-flex tw-flex-col tw-space-y-2">
              <div className="tw-p-3 md:tw-p-4 !tw-pb-0  tw-border-gray-100">
                <SearchBar type="page" handleSearch={(e) => setSearchTerm(e)} />
              </div>

              <div className="tw-flex-1 tw-overflow-y-auto">
                <ScrollPagination
                  key={`tl-${debouncedSearchTerm}-${reloadTrigger}-12`}
                  // itemList={itemList}
                  loadPage={loadPages}
                  renderItem={(page, index) => (
                    <div key={page?.id} className="tw-mb-3">
                      <PageLibraryItem page={page} />
                    </div>
                  )}
                  pageSize={12}
                  // useWindow={false}
                  className="tw-p-3 md:tw-p-4 tw-space-y-3"
                  emptyScreen={
                    <div className="tw-text-center tw-py-8">
                      <FileText className="tw-w-12 tw-h-12 tw-text-gray-400 tw-mx-auto tw-mb-4" />
                      <p className="tw-text-gray-500 tw-mb-2">No pages found</p>
                      <p className="tw-text-sm tw-text-gray-400">
                        {searchTerm
                          ? "Try adjusting your search"
                          : "Create pages first to add them to templates"}
                      </p>
                    </div>
                  }
                  emptyDescription="No pages available"
                  refreshKey={reloadTrigger}
                  filterIds={usedPageIds}
                />
              </div>
            </div>
          </>
        )}
      </div>
    </>
  );
};

export default TemplateLibrary;
