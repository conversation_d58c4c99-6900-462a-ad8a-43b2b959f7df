import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import { ChevronLeft, Plus, Settings } from "lucide-react";
import { useState, useEffect, useRef } from "react";
import { useDrop, useDragLayer } from "react-dnd";
import { DND_TYPES } from "../../../util/content";
import PageSetting from "./PageSetting";
import { deviceConfigs } from "../../Components/content";
import useHttp from "../../../hooks/use-http";
import { CONSTANTS } from "../../../util/constant/CONSTANTS";
import { apiGenerator } from "../../../util/functions";
import runtimeCache from "../../../utils/runtimeCache"; // Runtime cache utility

// Device sizes for responsive preview (similar to demo)
const DEVICE_SIZES = {
  mobile: { width: 375, height: 667 },
  tablet: { width: 768, height: 1024 },
  laptop: { width: 1200, height: 800 },
};

const PagePreview = ({
  pageData,
  setPageData,
  components,
  handleSave,
  saving,
  onCancel,
  isStructureOpen,
  setIsStructureOpen,
  isComponentOpen,
  setIsComponentOpen,
  generatePreviewHTML,
  isVersionChangeLoading,
  // setShowSettings,
}) => {
  const [showSettings, setShowSettings] = useState(false);
  const [previewMode, setPreviewMode] = useState("laptop");
  const [isDragging, setIsDragging] = useState(false);
  const deviceConfig = deviceConfigs(previewMode);
  const [scale, setScale] = useState(1);
  const api = useHttp();
  const containerRef = useRef(null);
  // console.log(pageData, "pageData in PagePreview");
  // Get current device dimensions
  const { width: deviceWidth, height: deviceHeight } =
    DEVICE_SIZES[previewMode];
  // Global drag detection
  const { isDraggingGlobal } = useDragLayer((monitor) => ({
    isDraggingGlobal: monitor.isDragging(),
  }));

  // Update local dragging state based on global drag state
  useEffect(() => {
    if (isDraggingGlobal) {
      setIsDragging(true);
    } else {
      // Small delay to allow drop to complete
      const timer = setTimeout(() => setIsDragging(false), 100);
      return () => clearTimeout(timer);
    }
  }, [isDraggingGlobal]);

  // const generatePreviewHTML = () => {
  //   return generateGlobalPreviewHTML({
  //     type: "page",
  //     data: pageData.componentData,
  //     pageData,
  //     customCSS: pageData.custom_css,
  //     customJS: pageData.custom_js,
  //     components,
  //     title: pageData?.meta_title || pageData?.name || "Page Preview",
  //   });
  // };

  const addComponentToPage = (component) => {
    // console.log("Adding component to page:", component, pageData);

    // First add the component with basic info
    const newPageComponent = {
      ...component,
      id: component?.id,
      name: component?.name,
      index: pageData?.componentData?.length,
      // version: component?.version || 1,
      componentVersionId: component?.version?.id,
      // repeator: "single",
      type: "single",
      class: "",
      uniqueId: `${component?.id}-${Date.now()}-${Math.random()
        .toString(36)
        .substring(2, 11)}`, // Unique ID for React keys
      // placeholders: component.placeholders,
      isLoading: true, // Add loading state
    };

    const updatedComponents = [...pageData?.componentData, newPageComponent];
    // console.log("Updated components:", updatedComponents);

    // setPageData((prevData) => ({
    //   ...prevData,
    //   componentData: updatedComponents,
    // }));

    // Check if full component data is cached
    const cachedComponent = runtimeCache.getCachedComponent(
      component?.id,
      component?.version?.version || 1
    );
    // console.log(cachedComponent, "cachedComponent");
    if (cachedComponent && cachedComponent.html_content) {
      // console.log(
      //   "Full component data loaded from runtime cache:",
      //   cachedComponent
      // );

      // Update the component with full content from cache
      setPageData((prevData) => ({
        ...prevData,
        componentData: [
          ...prevData?.componentData,
          { ...newPageComponent, ...cachedComponent, isLoading: false },
        ],
        // prevData.componentData?.length
        //   ? prevData.componentData.map((comp) =>
        //       comp?.id === newPageComponent?.id
        //         ? {
        //             ...comp,
        //             ...cachedComponent,
        //             isLoading: false,
        //           }
        //         : comp
        //     )
        //   : cachedComponent,
      }));
      return;
    }

    // Fetch full component data from API if not cached
    api.sendRequest(
      apiGenerator(CONSTANTS.API.componentVersions.getById, {
        id: component?.version?.id,
      }),
      (res) => {
        const fullComponentData = res?.data?.[0] || res;
        // console.log("Full component data fetched from API:", res, {
        //   ...pageData,
        //   componentData: pageData?.componentData?.length
        //     ? pageData?.componentData?.map((comp) =>
        //         comp?.id == newPageComponent?.id
        //           ? {
        //               ...comp,
        //               ...fullComponentData,
        //             }
        //           : comp
        //       )
        //     : [fullComponentData],
        // });

        // Cache the full component data
        runtimeCache.cacheComponent(fullComponentData);
        // console.log(
        //   "Full component data cached:",
        //   newPageComponent,
        //   pageData,
        //   fullComponentData
        // );
        // Update the component with full content
        setPageData((prevData) => ({
          ...prevData,
          componentData: [
            ...prevData?.componentData,
            { ...newPageComponent, ...fullComponentData, isLoading: false },
          ],
          // prevData?.componentData?.length
          //   ? prevData?.componentData?.map((comp) =>
          //       comp?.id == newPageComponent?.id
          //         ? {
          //             ...comp,
          //             ...newPageComponent,
          //             ...fullComponentData,
          //           }
          //         : comp
          //     )
          //   : [{ ...newPageComponent, ...fullComponentData }],
        }));
      },
      null,
      null,
      (error) => {
        console.error("Error fetching full component data:", error);
        // Remove loading state even on error
        setPageData((prevData) => ({
          ...prevData,
          // componentData: []
          // prevData?.componentData?.length
          //   ? prevData?.componentData?.map((comp) =>
          //       comp?.id == newPageComponent?.id
          //         ? { ...comp, isLoading: false }
          //         : comp
          //     )
          //   : [],
        }));
      }
    );
  };

  // Preview Drop Zone Component - Improved for library components (NO debouncing)
  const PreviewDropZone = ({ isDragging, setIsDragging }) => {
    const [{ isOver, canDrop }, drop] = useDrop(
      () => ({
        accept: DND_TYPES.LIB_ITEM,
        drop: (item, monitor) => {
          // Immediate drop without debouncing for library components
          if (monitor?.didDrop()) return; // Prevent duplicate drops
          // console.log(item, "Dropped item in PreviewDropZone");
          addComponentToPage(item?.component);
          setIsDragging(false); // Reset dragging state after drop
        },
        collect: (monitor) => ({
          isOver: monitor.isOver({ shallow: true }),
          canDrop: monitor.canDrop(),
        }),
        hover: (_, monitor) => {
          // Set dragging state when hovering
          if (monitor.canDrop()) {
            setIsDragging(true);
          }
        },
      }),
      [pageData, setIsDragging] // Dependency on components to ensure fresh state
    );

    const showDropIndicator = isOver && canDrop;

    return (
      <div
        ref={drop}
        className={`tw-absolute tw-inset-0 tw-transition-all tw-duration-150 ${
          isDragging || showDropIndicator ? "tw-z-30" : "tw-z-10"
        } ${
          showDropIndicator
            ? "tw-bg-blue-50/60 tw-border-2 tw-border-dashed tw-border-blue-400"
            : ""
        }`}
        style={{
          minHeight: "400px",
          pointerEvents: "auto", // Always allow pointer events for drop zone
        }}
        onMouseLeave={() => {
          // Reset dragging state when mouse leaves the drop zone
          if (!isOver) {
            setIsDragging(false);
          }
        }}
      >
        {showDropIndicator && (
          <div className="tw-flex tw-items-center tw-justify-center tw-h-full tw-pointer-events-none">
            <div className="tw-bg-white tw-rounded-lg tw-p-6 tw-shadow-xl tw-border-2 tw-border-blue-300 tw-animate-pulse">
              <div className="tw-flex tw-items-center tw-space-x-3">
                <div className="tw-w-3 tw-h-3 tw-bg-blue-500 tw-rounded-full tw-animate-bounce"></div>
                <p className="tw-text-blue-700 tw-font-semibold tw-text-lg">
                  Drop to add component
                </p>
                <div className="tw-w-3 tw-h-3 tw-bg-blue-500 tw-rounded-full tw-animate-bounce tw-animation-delay-100"></div>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  // Scale calculation function (similar to demo)
  const recalcScale = () => {
    if (!containerRef.current) return;
    const bounds = containerRef.current.getBoundingClientRect();
    // Add padding to ensure device doesn't fill entire container
    const availableWidth = bounds.width - 30; // 20px padding on each side
    const availableHeight = bounds.height - 30; // 20px padding on top/bottom
    const widthScale = availableWidth / deviceWidth;
    const heightScale = availableHeight / deviceHeight;
    setScale(Math.min(widthScale, heightScale, 1)); // Don't scale up beyond 100%
  };

  // Update scale on mount & when device changes
  useEffect(() => {
    recalcScale();
    const resizeObserver = new ResizeObserver(recalcScale);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }
    return () => resizeObserver.disconnect();
  }, [previewMode, deviceWidth, deviceHeight]);

  return (
    <>
      {/* Main Canvas Area */}

      {/* Top Toolbar */}
      <div className="tw-flex tw-w-full tw-bg-white tw-border-b tw-border-gray-200 tw-space-x-4">
        {!isComponentOpen && (
          <div className=" tw-flex tw-items-center tw-justify-center tw-border-r tw-border-gray-200">
            <Tooltip
              title={
                isComponentOpen ? "Hide Page Structure" : "Show Page Structure"
              }
            >
              <button
                onClick={() => setIsComponentOpen((v) => !v)}
                className="tw-text-gray-600 tw-px-3 tw-flex tw-items-center tw-justify-center tw-rounded-lg"
              >
                <ChevronLeft
                  size={30}
                  className={` ${isComponentOpen ? "" : "tw-rotate-180 "}`}
                />
              </button>
            </Tooltip>
          </div>
        )}
        <div className="tw-p-3 md:tw-p-[21px] tw-flex tw-w-full tw-items-center tw-justify-between">
          <div className="tw-flex tw-items-center tw-space-x-4">
            <div className="tw-rounded-lg tw-hidden md:tw-flex">
              <div className="tw-flex tw-items-center tw-space-x-1  tw-rounded-lg  tw-mb-1">
                {Object.entries(deviceConfig)?.map(([key, config]) => (
                  <Tooltip
                    key={key}
                    title={`${config?.label} (${config?.description})`}
                  >
                    <button
                      type="button"
                      onClick={() => setPreviewMode(key)}
                      className={`tw-flex tw-items-center tw-justify-center tw-w-8 tw-h-8 tw-rounded-md tw-transition-all tw-duration-200 ${
                        previewMode === key
                          ? "tw-bg-[#EAF0FD] tw-text-white tw-shadow-sm"
                          : "tw-text-gray-500 tw-hover:tw-bg-gray-100 tw-hover:tw-text-font-color"
                      }`}
                    >
                      {config.icon}
                    </button>
                  </Tooltip>
                ))}
              </div>
            </div>

            <button
              onClick={() => setShowSettings(true)}
              className="tw-flex tw-items-center tw-px-3 tw-py-2 tw-text-gray-600 tw-hover:tw-bg-gray-100 tw-rounded-lg tw-transition-colors"
            >
              <Tooltip title={`Page Settings`}>
                <Settings className="tw-w-4 tw-h-4 tw-mr-2" />
              </Tooltip>
            </button>
          </div>

          <div className="tw-flex tw-items-center tw-space-x-2">
            <Button
              onClick={onCancel}
              type="text"
              className="tw-px-4 tw-py-2 tw-text-gray-600 tw-hover:tw-bg-gray-100 tw-rounded-lg tw-transition-colors"
            >
              Cancel
            </Button>

            <Button
              type="primary"
              size="large"
              onClick={handleSave}
              disabled={saving}
              loading={saving}
              // || !pageData.name || !pageData.slug}
              className="tw-px-6 tw-h-10 tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
            >
              {/* {saving ? (
                <>
                  <div className="tw-animate-spin tw-rounded-full tw-h-4 tw-w-4 tw-border-b-2 tw-border-white tw-mr-2"></div>
                  Saving...
                </>
              ) : ( */}
              <>Save</>
              {/* )} */}
            </Button>
          </div>
        </div>
        {/* Toggle Page Structure inside the same control group */}
        {!isStructureOpen && (
          <div className=" tw-flex tw-items-center tw-justify-center tw-border-l tw-border-gray-200">
            <Tooltip
              title={
                isStructureOpen ? "Hide Page Structure" : "Show Page Structure"
              }
            >
              <button
                onClick={() => setIsStructureOpen((v) => !v)}
                className="tw-text-gray-600 tw-px-3 tw-flex tw-items-center tw-justify-center tw-rounded-lg"
              >
                <ChevronLeft
                  size={30}
                  className={` ${!isStructureOpen ? "" : "tw-rotate-180 "}`}
                />
              </button>
            </Tooltip>
          </div>
        )}
      </div>

      {/* Canvas */}
      <div className="tw-p-1 md:tw-p-4 tw-w-full tw-h-full tw-bg-gray-50">
        {/* Preview container */}
        <div
          ref={containerRef}
          className="tw-w-full tw-h-[34rem] tw-bg-gray-100 tw-flex tw-justify-center tw-items-center tw-overflow-hidden tw-relative tw-rounded-lg"
        >
          {/* Virtual device */}
          <div
            className="tw-bg-white tw-p-4 tw-rounded-xl tw-border tw-border-4 tw-border-dashed tw-border-gray-300 tw-absolute"
            style={{
              width: `${deviceWidth}px`,
              height: `${deviceHeight}px`,
              transform: `scale(${scale})`,
              left: "50%",
              top: "50%",
              marginLeft: `-${deviceWidth / 2}px`,
              marginTop: `-${deviceHeight / 2}px`,
              transition: "transform 0.3s ease",
              // border: "10px solid transparent",
              // borderImage: "url('data:image/svg+xml;utf8,<svg width='10' height='10' viewBox='0 0 10 10' xmlns='http://www.w3.org/2000/svg'><line x1='0' y1='5' x2='5' y2='5' stroke='black' stroke-width='2' stroke-dasharray='2 3'/></svg>') 10 round";
            }}
          >
            <div className="tw-relative tw-w-full tw-h-full tw-flex tw-flex-col tw-overflow-hidden tw-rounded-xl">
              {/* Loading indicator overlay */}
              {(api?.isLoading || isVersionChangeLoading) && (
                <div className="tw-absolute tw-inset-0 tw-bg-white tw-bg-opacity-75 tw-flex tw-items-center tw-justify-center tw-z-50 tw-rounded-xl">
                  <div className="tw-text-center">
                    <div className="preview-loader-spinner tw-mx-auto tw-mb-4"></div>
                  </div>
                </div>
              )}

              {pageData?.componentData?.length ? (
                <iframe
                  srcDoc={generatePreviewHTML()}
                  className={`tw-w-full tw-h-full tw-border-0 tw-rounded-xl ${
                    isDragging ? "tw-z-0" : "tw-z-20"
                  }`}
                  title="Page Preview"
                  style={{
                    pointerEvents: isDragging ? "none" : "auto",
                    background: "#fff",
                  }}
                />
              ) : (
                <div className="tw-flex tw-items-center tw-justify-center tw-h-full tw-p-6">
                  <div className="tw-text-center">
                    <Plus className="tw-w-12 tw-h-12 tw-text-gray-400 tw-mx-auto tw-mb-4" />
                    <p className=" tw-mb-2 tw-text-xl tw-text-font-color">
                      Drop components here
                    </p>
                    <p className="tw-text-sm tw-text-gray-500">
                      Drag components from the left panel to build your page
                    </p>
                  </div>
                </div>
              )}
              <PreviewDropZone
                isDragging={isDragging}
                setIsDragging={setIsDragging}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Page Settings Modal */}
      {showSettings && (
        <PageSetting
          showSettings={showSettings}
          setShowSettings={setShowSettings}
          pageData={pageData}
          setPageData={setPageData}
        />
      )}
    </>
  );
};

export default PagePreview;
