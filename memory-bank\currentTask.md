# Task: Create the main layout for the Website Manager page

- Create a new file `src/components/Websites/WebsiteManager.jsx`.
- Add a main container div with appropriate padding and background color.
- Use a flexbox or grid layout to structure the page.

# Task: Implement the header section with the title and "Create Website" button

- Add a header section at the top of the page.
- Include an `h1` tag for the title "Websites".
- Add an Ant Design Button component for "Create Website".
- Use a flexbox layout to align the title and button.

# Task: Add a search bar for filtering websites

- Add an Ant Design Input.Search component below the header.
- Implement state to manage the search query.
- Use the `useDebounce` hook to delay API calls while the user is typing.

# Task: Create a card component to display website information

- Create a new file `src/components/Websites/components/WebsiteCard.jsx`.
- The card will display:
  - Status (e.g., "Draft", "Published")
  - Version
  - Website Name
  - Template Name
  - Template Version
  - Action buttons (Duplicate, Edit, Delete)
  - Export button
- Use Ant Design Card component as the base.
- Use flexbox for layout within the card.

# Task: Implement a grid layout to display the website cards

- In `WebsiteManager.jsx`, use a grid layout (e.g., Tailwind CSS grid) to display the `WebsiteCard` components.
- The grid should be responsive, adjusting the number of columns based on the screen size.

# Task: Integrate a scroll pagination component to handle large lists of websites

- Import and use the `ScrollPagination` component.
- Pass the necessary props: `data`, `total`, `fetchData`, `renderItem`.
- The `renderItem` function will return the `WebsiteCard` component.

# Task: Create a modal for duplicating a website

- Create a new file `src/components/Websites/components/DuplicateWebsiteModal.jsx`.
- Use the Ant Design Modal component.
- The modal will contain a form with fields for "Website Name" and "Version".
- Use the dynamic form generation pattern with a configuration in `CONSTANTS.js`.

# Task: Add action buttons (duplicate, edit, delete) to the website cards

- In `WebsiteCard.jsx`, add `Duplicate`, `Edit`, and `Delete` icons using `lucide-react`.
- Implement `onClick` handlers for each button.
- The duplicate button will open the `DuplicateWebsiteModal`.

# Task: Implement the preview functionality using Ant Design components

- This will be implemented later, likely involving a modal or a separate preview page.

# API Integration Pseudocode

## GET all websites

- In `WebsiteManager.jsx`:
- Instantiate `useHttp` hook.
- Use `useEffect` to fetch websites on component mount.
- Implement a `fetchWebsites` function that calls `api.sendRequest`.
- Use `CONSTANTS.API.websites.get`.
- Handle the response to update the websites state and total count for pagination.

## Create website

- The "Create Website" button will likely navigate to a new page for creating a website.
- This will be implemented as part of a separate task.

## Duplicate website

- In `DuplicateWebsiteModal.jsx`:
- On form submission, call `api.sendRequest`.
- Use `CONSTANTS.API.websites.duplicate`.
- Pass the website ID and the new name/version in the payload.
- On success, close the modal and refresh the website list.

## Update website

- The "Edit" button will likely navigate to a page for editing the website.
- This will be implemented as part of a separate task.

## Delete website

- In `WebsiteCard.jsx`:
- On delete button click, show a confirmation modal.
- On confirmation, call `api.sendRequest`.
- Use `CONSTANTS.API.websites.delete`.
- Pass the website ID.
- On success, refresh the website list.
