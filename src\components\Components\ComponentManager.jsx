import React, { useState, useEffect, useCallback } from "react";
import ComponentEditor from "./ComponentEditor";
import {
  Plus,
  Component as Components,
  Edit2,
  Trash2,
  Eye,
  Search,
  Filter,
  Loader2,
  Pencil,
} from "lucide-react";
import useHttp from "../../hooks/use-http";
import useDebounce from "../../hooks/useDebounce";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { apiGenerator, autoDetectPlaceholders } from "../../util/functions";
import { useAuth } from "../../contexts/AuthContext";
import {
  Select,
  Tag,
  Input,
  Button,
  message,
  Tooltip,
  Popconfirm,
  Typography,
} from "antd";
import ScrollPagination from "../common/ScrollPagination";
import { getPreviewHTML } from "./content";
import { useLocation, useNavigate } from "react-router-dom";
import CustomTooltip from "../common/CustomTooltip";
import CommonButton from "../common/CommonButton";
import { ROUTES } from "../../util/Route";
const { Title, Text, Paragraph } = Typography;

const ComponentManager = () => {
  const { user } = useAuth();

  const [categories, setCategories] = useState([]);
  const [globalVarible, setGlobalVarible] = useState([]);
  const [showEditor, setShowEditor] = useState(false);
  const [editingComponent, setEditingComponent] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const navigate = useNavigate();
  // Debounce search term to avoid excessive API calls or filtering
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // Track if search is in progress (when searchTerm !== debouncedSearchTerm)
  const isSearching = searchTerm !== debouncedSearchTerm;
  const api = useHttp(); // Commented for future API use
  const { isLoading } = api; // Extract loading state
  // const api = useStorage(); // Using JSON storage

  const [totalCount, setTotalCount] = useState(0);
  const pageSize = 12;
  const [patchedItem, setPatchedItem] = useState(null);
  const location = useLocation();
  useEffect(() => {
    if (location?.pathname == `/${ROUTES.components}` && showEditor) {
      setShowEditor(false);
      setEditingComponent(null);
    }
  }, [location]);

  useEffect(() => {
    api.sendRequest(CONSTANTS.API.globalVarible.get, (res) => {
      setGlobalVarible(res?.data?.variables?.split(",") || []);
    });
  }, []);

  // Server-side loader for infinite scroll
  const loadPage = useCallback(
    ({ page, pageSize: size }) =>
      new Promise((resolve, reject) => {
        const payload = {
          page,
          limit: size,
        };
        if (debouncedSearchTerm) {
          payload.search = debouncedSearchTerm;
        }
        if (selectedCategory) {
          payload.categoryId = selectedCategory;
        }
        api.sendRequest(
          CONSTANTS.API.components.get,
          (res) => {
            const rows = res?.data?.rows || [];
            const count = res?.data?.count || 0;
            setTotalCount(count);
            resolve({ items: rows, totalCount: count });
          },
          payload,
          null,
          (err) => reject(err)
        );
      }),
    [api, debouncedSearchTerm, selectedCategory]
  );

  const renderComponentCard = (component) => {
    const componentData = component?.componentversions?.[0] || component;
    const placeholder = autoDetectPlaceholders(componentData?.html);

    return (
      <div
        key={component?.id}
        className="tw-bg-white tw-h-full tw-rounded-xl tw-shadow-sm tw-border tw-border-gray-200 tw-hover:tw-shadow-md tw-transition-all tw-duration-200"
      >
        <div className="tw-p-4 tw-border-b tw-border-gray-100">
          <div className="tw-flex tw-items-center tw-justify-between tw-mb-2">
            <div className="tw-flex tw-items-center">
              <CustomTooltip title={component?.category?.name} key="category">
                <Tag
                  color="purple"
                  className="tw-rounded-xl tw-py-[2px] tw-max-w-[120px] tw-overflow-hidden tw-whitespace-nowrap tw-text-ellipsis"
                >
                  {component?.category?.name}
                </Tag>
              </CustomTooltip>
              <Tag color="default" className="tw-rounded-xl">
                v{component?.componentversions?.[0]?.version || 0}
              </Tag>
            </div>
            {user?.role === "admin" && (
              <div className="tw-flex tw-space-x-2">
                <Button
                  type="text"
                  icon={<Pencil className="tw-w-4 tw-h-4" />}
                  onClick={() => handleEdit(component)}
                  className="tw-p-2 tw-text-gray-400 tw-hover:tw-text-blue-600 tw-hover:tw-bg-blue-50 tw-rounded-lg tw-transition-colors"
                />
                <Tooltip title="Delete Component" key="delete">
                  <Popconfirm
                    title="Delete Component"
                    description="Are you sure you want to delete this component?"
                    onConfirm={() => handleDelete(component?.id)}
                    okText="Yes"
                    cancelText="No"
                    okButtonProps={{ danger: true }}
                  >
                    <Button
                      type="text"
                      danger
                      icon={<Trash2 className="tw-w-4 tw-h-4" />}
                      loading={isLoading}
                      disabled={isLoading}
                      className="tw-p-2 tw-text-gray-400 tw-hover:tw-text-red-600 tw-hover:tw-bg-red-50 tw-rounded-lg tw-transition-colors"
                    />
                  </Popconfirm>
                </Tooltip>
              </div>
            )}
          </div>
          <div className="tw-flex tw-items-center tw-space-x-2 tw-justify-between">
            {/* <h3 className="tw-text-lg tw-font-semibold tw-text-gray-900 tw-truncate"> */}
            <Title
              ellipsis={{ rows: 1, expandable: false, tooltip: true }}
              level={4}
              className="tw-text-lg tw-font-semibold tw-text-gray-900 tw-truncate"
            >
              {component?.name}
            </Title>
            {/* </h3> */}
            <span className="tw-text-gray-500 tw-text-sm">
              {new Date(component?.createdAt).toLocaleDateString()}
            </span>
          </div>
        </div>
        <div className="tw-p-4">
          <div
            className="tw-bg-gray-50 tw-rounded-lg tw-p-3 tw-mb-4 tw-max-h-52 tw-relative tw-overflow-hidden"
            style={{ height: "208px" }}
          >
            <iframe
              srcDoc={getPreviewHTML([component])}
              className="tw-w-full tw-h-full tw-border-0 tw-rounded-lg tw-origin-top-left"
              title="Component Preview"
              style={{
                transform: "scale(0.5)",
                width: "200%",
                height: "200%",
                transformOrigin: "top left",
              }}
            />
          </div>
          {placeholder && placeholder?.length > 0 && (
            <div className="tw-mb-4">
              <p className="tw-text-sm tw-font-medium tw-text-font-color tw-mb-2">
                Placeholders:
              </p>
              <div className="tw-flex tw-flex-wrap tw-gap-1 tw-gap-y-2">
                {placeholder?.slice(0, 3)?.map((ph, index) => (
                  <Tag className="tw-rounded-full" color="blue" key={index}>
                    {`$${ph}`}
                  </Tag>
                  // <span
                  //   key={index}
                  //   className="tw-px-2 tw-py-1 tw-bg-blue-100 tw-text-blue-800 tw-text-xs tw-rounded-full"
                  // >
                  //   {`$${ph}`}
                  // </span>
                ))}
                {placeholder?.length > 3 && (
                  <Tag
                    className="tw-rounded-full tw-text-gray-500"
                    // color="gray"
                    key={"more"}
                  >
                    +{placeholder?.length - 3} more
                  </Tag>
                  // <span className="tw-px-2 tw-py-1 tw-bg-gray-100 tw-text-gray-600 tw-text-xs tw-rounded-full">
                  //   +{placeholder?.length - 3} more
                  // </span>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  useEffect(() => {
    api.sendRequest(CONSTANTS.API.categories.getCategoryDropDownList, (res) => {
      setCategories(res?.data?.rows || []);
    });
  }, []);

  const handleEdit = (component) => {
    navigate(`/components/${component?.id}`);
    setEditingComponent({
      ...component,
      html: component?.componentversions?.[0]?.html || "",
      css: component?.componentversions?.[0]?.css || "",
      js: component?.componentversions?.[0]?.js || "",
      // placeholders: component?.placeholders || [],
      // repeatedPlaceholder: component?.repeatedPlaceholder || [],
    });
    setShowEditor(true);
  };

  const handleDelete = async (id) => {
    api.sendRequest(
      apiGenerator(CONSTANTS.API.components.delete, { id }),
      () => {
        setPatchedItem({ id, type: "delete" });
        message.success("Component deleted successfully!");
      },
      null,
      null,
      (error) => {
        console.error("Delete failed", error);
        message.error("Failed to delete component");
      }
    );
  };

  const handleSave = async (savedItem) => {
    // Enrich with category_name if needed
    const cat = categories?.find((c) => c?.id === savedItem?.categoryId);

    // Ensure all required fields are present for proper card rendering
    const enriched = {
      ...savedItem,
      // Add category name if found
      category_name: cat?.name || "Unknown Category",
      // Ensure componentversions array exists for version display
      componentversions: savedItem?.componentversions || [
        {
          id: savedItem?.id || Date.now(),
          version: 1,
        },
      ],
      // Ensure createdAt exists for date display
      createdAt: savedItem?.createdAt || new Date().toISOString(),
      // Ensure updatedAt exists
      updatedAt: savedItem?.updatedAt || new Date().toISOString(),
      // Ensure html field exists for preview
      html: savedItem?.html || "",
      // Ensure css field exists
      css: savedItem?.css || "",
      // Ensure js field exists
      js: savedItem?.js || "",
      // Add any other missing fields that might be needed
      isActive: savedItem?.isActive !== undefined ? savedItem?.isActive : true,
    };

    console.log("Enriched saved item:", enriched);

    // setPatchedItem({
    //   ...enriched,
    //   type: editingComponent ? "update" : "create",
    // });
    setShowEditor(false);
    setEditingComponent(null);
  };

  if (showEditor) {
    return (
      <div className="">
        <ComponentEditor
          component={editingComponent}
          categories={categories}
          onSave={handleSave}
          globalVarible={globalVarible}
          onCancel={() => {
            navigate("/components");
            setShowEditor(false);
            setEditingComponent(null);
          }}
        />
      </div>
    );
  }

  const addHandler = () => {
    navigate("/components/add");
    setShowEditor(true);
  };

  return (
    <div className="tw-p-6">
      <div className="tw-flex tw-flex-col tw-lg:tw-flex-row tw-justify-between tw-items-start tw-lg:tw-items-center tw-mb-6 tw-space-y-4 tw-lg:tw-space-y-0">
        <div className="tw-flex tw-items-center tw-justify-between tw-w-full">
          <h2 className="tw-text-xl tw-font-bold tw-text-gray-900">
            Components ({totalCount})
          </h2>
          {user?.role === "admin" && (
            <CommonButton
              onClick={addHandler}
              icon={<Plus className="tw-w-4 tw-h-4 tw-mr-2" />}
              text="Add Component"
            />
            // <Button
            //   type="primary"
            //   size="large"
            //   onClick={addHandler}
            //   icon={<Plus className="tw-w-4 tw-h-4 tw-mr-2" />}
            //   className="tw-px-6 tw-h-10 tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
            // >
            //   Add Component
            // </Button>
          )}
        </div>

        <div className="tw-flex tw-items-center tw-justify-between tw-w-full tw-space-x-4">
          <div className="tw-w-full ">
            <Input
              placeholder="Search components..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              prefix={
                isSearching ? (
                  <Loader2 className="tw-w-4 tw-h-4 tw-text-blue-500 tw-animate-spin" />
                ) : (
                  <Search className="tw-w-4 tw-h-4 tw-text-gray-400" />
                )
              }
              allowClear
              className="search-input-enhanced"
              size="middle"
              style={{
                borderRadius: "8px",
              }}
            />
          </div>
          <div className="tw-flex tw-items-center">
            <Select
              prefix={
                <Filter width={16} height={16} className=" tw-text-gray-400" />
              }
              value={selectedCategory}
              onChange={setSelectedCategory}
              style={{ width: 200 }}
              placeholder="Select Category"
            >
              <Select.Option value="">All Categories</Select.Option>
              {categories?.map((category) => (
                <Select.Option key={category.id} value={category.id}>
                  {category.name?.trim()}
                </Select.Option>
              ))}
            </Select>
          </div>
        </div>
      </div>

      <ScrollPagination
        key={`cmp-${debouncedSearchTerm}-${selectedCategory}-${pageSize}`}
        loadPage={loadPage}
        updatedItem={patchedItem}
        idKey="id"
        renderItem={(component) => (
          <div key={component?.id}>{renderComponentCard(component)}</div>
        )}
        pageSize={pageSize}
        useWindow
        className="tw-grid tw-grid-cols-1 md:tw-grid-cols-2 lg:tw-grid-cols-2 tw-gap-4"
        emptyScreen={
          <div className="tw-w-full tw-h-fit tw-flex tw-items-center tw-justify-center tw-mt-[20%]">
            <div className="tw-text-center tw-pt-30 tw-h-full">
              <Components className="tw-w-16 tw-h-16 tw-text-gray-400 tw-mx-auto tw-mb-4" />
              <h3 className="tw-text-lg  tw-font-semibold tw-text-gray-900 tw-mb-2">
                {debouncedSearchTerm || selectedCategory
                  ? "No components found"
                  : "No components yet"}
              </h3>
              <p className="tw-text-gray-500 tw-mb-4">
                {debouncedSearchTerm || selectedCategory
                  ? "Try adjusting your search or filter criteria"
                  : "Create your first reusable component to get started"}
              </p>
            </div>
          </div>
        }
      />
    </div>
  );
};

export default ComponentManager;
