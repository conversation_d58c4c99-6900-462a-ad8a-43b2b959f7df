import React, { useEffect, useRef, useState, useCallback } from "react";
import { FileText } from "lucide-react";
import { generateGlobalPreviewHTML } from "../../Components/content";

// Device sizes for responsive preview
const DEVICE_SIZES = {
  mobile: { width: 375, height: 667 },
  tablet: { width: 768, height: 1024 },
  laptop: { width: 1200, height: 800 },
};

const SinglePagePreview = ({
  templatePage,
  previewMode,
  isDragging,
  isLoading,
  onComponentClick,
  onPageClick,
  setLoadingPreviews,
  generatePreview,
  // contentJSON,
  // dynamicContent = null,
  /** New: allow upscaling beyond 1 on big screens */
  maxScale = 1.8,
  /** New: adjustable padding around device frame */
  padding = 20,
}) => {
  const containerRef = useRef(null);
  const iframeRef = useRef(null);
  // console.log("single page preview...");
  const [scale, setScale] = useState(1);
  // Get current device dimensions
  const { width: deviceWidth, height: deviceHeight } =
    DEVICE_SIZES[previewMode];

  // Scale calculation function (similar to PagePreview)
  const recalcScale = () => {
    if (!containerRef.current) return;
    const bounds = containerRef.current.getBoundingClientRect();
    // Add padding to ensure device doesn't fill entire container
    const padding = 20; // total padding to keep device from touching edges
    const availableWidth = Math.max(0, bounds.width - padding);
    const availableHeight = Math.max(0, bounds.height - padding);
    const widthScale = availableWidth / deviceWidth;
    const heightScale = availableHeight / deviceHeight;

    // allow upscaling up to maxScale (previously hard-capped at 1)
    const nextScale = Math.min(widthScale, heightScale, maxScale);
    setScale(nextScale);
    // requestAnimationFrame(() => setScale(nextScale));
    // requestAnimationFrame(() => {
    //   setScale(Math.min(widthScale, heightScale, 1)); // Don't scale up beyond 100%
    // });
  };

  // Handle postMessage from iframe
  const handleIframeMessage = useCallback(
    (event) => {
      // Security check - ensure message is from our iframe
      if (event.source !== iframeRef.current?.contentWindow) return;

      const { type, data } = event.data || {};
      // console.log("📨 Iframe message received:", { type, data });

      switch (type) {
        case "COMPONENT_CLICK":
          if (onComponentClick) {
            // console.log("🎯 Forwarding component click to handler", data);
            onComponentClick(data);
          }
          break;
        case "PAGE_CLICK":
          if (onPageClick) {
            // console.log("📄 Forwarding page click to handler");
            onPageClick(data);
          }
          break;
        default:
          console.log("❓ Unknown message type:", type);
          break;
      }
    },
    [onComponentClick, onPageClick]
  );

  useEffect(() => {
    recalcScale();
    const resizeObserver = new ResizeObserver(recalcScale);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }
    return () => resizeObserver.disconnect();
  }, [previewMode, deviceWidth, deviceHeight]);

  // Update scale on mount & when device changes
  // useEffect(() => {
  //   recalcScale();
  //   const ro = new ResizeObserver(recalcScale);
  //   if (containerRef.current) ro.observe(containerRef.current);

  //   // Debounced resize handler to prevent excessive re-renders
  //   let resizeTimeout;
  //   const onWinResize = () => {
  //     clearTimeout(resizeTimeout);
  //     resizeTimeout = setTimeout(() => {
  //       recalcScale();
  //     }, 100); // Debounce resize events
  //   };
  //   window.addEventListener("resize", onWinResize);

  //   return () => {
  //     ro.disconnect();
  //     window.removeEventListener("resize", onWinResize);
  //     clearTimeout(resizeTimeout);
  //   };
  // }, [previewMode, deviceWidth, deviceHeight]);

  // Add postMessage listener
  useEffect(() => {
    window.addEventListener("message", handleIframeMessage);
    return () => {
      window.removeEventListener("message", handleIframeMessage);
    };
  }, [handleIframeMessage]);

  // const generatePreview = useCallback(
  //   (tempData) => {
  //     setLoadingPreviews((pr) => !pr);
  //     try {
  //       // Process components and replace {{key}} placeholders with actual component data
  //       const processedComponents = tempData?.componentData?.map(
  //         (component) => {
  //           if (component.type === "repeat" && component?.repeatComponents) {
  //             // Create a copy of the component to avoid mutating original
  //             let processedComponent = { ...component };

  //             // Replace {{key}} placeholders in component HTML with actual component data
  //             if (component?.html) {
  //               let processedHTML = component?.html;

  //               // Iterate through all repeat components and replace their placeholders
  //               component?.repeatComponents?.forEach((repeatComp) => {
  //                 if (repeatComp?.key && repeatComp?.html) {
  //                   // Replace the placeholder with the actual component HTML
  //                   const regex = new RegExp(
  //                     `\\{\\{${repeatComp?.key}\\}\\}`,
  //                     "g"
  //                   );
  //                   processedHTML = processedHTML?.replace(
  //                     regex,
  //                     repeatComp?.html
  //                   );
  //                 }
  //               });

  //               processedComponent.html = processedHTML;
  //             }

  //             return processedComponent;
  //           }

  //           return component;
  //         }
  //       );

  //       const result = generateGlobalPreviewHTML({
  //         type: "page",
  //         data: processedComponents,
  //         pageData: tempData,
  //         // components,
  //       });

  //       return result;
  //     } catch (error) {
  //       console.log(error, "error");
  //     } finally {
  //       setLoadingPreviews((pr) => !pr);
  //     }
  //   },
  //   [templatePage]
  // );
  // console.log(templatePage, "templatePage");
  // Template Page Preview Component - Single device frame like PagePreview

  return (
    <div
      ref={containerRef}
      className={`tw-overflow-hidden tw-relative tw-rounded-lg   tw-w-full tw-h-[450px]
             `}
    >
      <div
        className={`tw-rounded-xl tw-flex tw-justify-center tw-items-center tw-relative `}
        style={{
          height: "100%",
        }}
      >
        {/* Virtual device frame - exactly like PagePreview */}
        <div
          className="device-frame tw-bg-white tw-rounded-xl  tw-border tw-border-gray-200 tw-absolute"
          style={{
            width: `${deviceWidth}px`,
            height: `${deviceHeight}px`,
            left: "50%",
            top: "50%",
            transform: `translate(-50%, -50%) scale(${scale})`,
            transition: "all 0.3s ease",
            zIndex: isDragging ? 0 : 25,
          }}
        >
          <div className="tw-relative tw-w-full tw-h-full tw-flex tw-flex-col tw-rounded-xl">
            {isLoading ? (
              <div className="tw-absolute tw-inset-0 tw-bg-white tw-bg-opacity-75 tw-flex tw-items-center tw-justify-center tw-z-50 tw-rounded-xl">
                <div className="tw-text-center">
                  <div className="preview-loader-spinner tw-mx-auto tw-mb-4"></div>
                </div>
              </div>
            ) : (
              ""
            )}
            {templatePage ? (
              <iframe
                ref={iframeRef}
                // srcDoc={generatePreview(templatePage)}
                srcDoc={templatePage?.full_page_content}
                className="tw-w-full tw-h-full tw-border-0 tw-rounded-xl tw-relative"
                title={`${templatePage?.name} Preview`}
                style={{
                  pointerEvents: isDragging ? "none" : "auto",
                  background: "#fff",
                  opacity: isLoading ? 0.7 : 1,
                  transition: "opacity 0.2s ease-in-out",
                }}
                onLoad={() => {
                  // Ensure iframe is fully loaded before showing
                  if (iframeRef.current) {
                    iframeRef.current.style.opacity = "1";
                  }
                }}
              />
            ) : (
              <div className="tw-flex tw-items-center tw-justify-center tw-h-full ">
                <div className="tw-text-center">
                  <FileText className="tw-w-12 tw-h-12 tw-text-gray-400 tw-mx-auto tw-mb-4" />
                  <p className="tw-text-gray-500 tw-mb-2">Page not found</p>
                  <p className="tw-text-sm tw-text-gray-400">
                    This page may have been deleted
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      {/* <TemplatePagePreview key={templatePage?.id} templatePage={templatePage} /> */}
    </div>
  );
};

export default SinglePagePreview;
