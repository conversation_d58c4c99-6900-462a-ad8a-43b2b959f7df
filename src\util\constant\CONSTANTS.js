

const userRole = localStorage.getItem("user") ? JSON.parse(localStorage.getItem("user"))?.role : null;

export const CONSTANTS = {
    API: {
        // Categories API endpoints
        categories: {
            get: {
                type: "GET",
                endpoint: "/admin/categories"
            },
            create: {
                type: "POST",
                endpoint: "/admin/categories"
            },
            update: {
                type: "PATCH",
                endpoint: `/admin/categories/:id`
            },
            delete: {
                type: "DELETE",
                endpoint: "/admin/categories/:id"
            },
            getCategoryDropDownList: {
                type: "GET",
                endpoint: "/admin/categories/getCategoriesForDropdown"
            }
        },

        // Components API endpoints
        components: {
            get: {
                type: "GET",
                endpoint: "/admin/components"
            },
            getById: {
                type: "GET",
                endpoint: "/admin/components/:id"
            },
            create: {
                type: "POST",
                endpoint: "/admin/components"
            },
            update: {
                type: "PATCH",
                endpoint: "/admin/components/:id"
            },
            delete: {
                type: "DELETE",
                endpoint: "/admin/components/:id"
            },
            getAllForPage: {
                type: "GET",
                endpoint: "/admin/components/getForPage"
            }
        },
        componentVersions: {
            get: {
                type: "GET",
                endpoint: "/admin/componentversions"
            },
            getById: {
                type: "GET",
                endpoint: "/admin/componentversions/:id"
            },
            create: {
                type: "POST",
                endpoint: "/admin/componentversions"
            },
            update: {
                type: "PATCH",
                endpoint: "/admin/componentversions/:id"
            },
            delete: {
                type: "DELETE",
                endpoint: "/admin/componentversions/:id"
            }
        },

        // Pages API endpoints
        pages: {
            get: {
                type: "GET",
                endpoint: "/admin/pages"
            },
            getById: {
                type: "GET",
                endpoint: "/admin/pages/:id"
            },
            create: {
                type: "POST",
                endpoint: "/admin/pages"
            },
            update: {
                type: "PATCH",
                endpoint: "/admin/pages/:id"
            },
            delete: {
                type: "DELETE",
                endpoint: "/admin/pages/:id"
            },
            getAllForTemplate: {
                type: "GET",
                endpoint: "/admin/pages/getForTemplate"
            }
        },

        // Page Versions API endpoints
        pageVersions: {
            get: {
                type: "GET",
                endpoint: "/admin/pageversions"
            },
            getById: {
                type: "GET",
                endpoint: "/admin/pageversions/:id"
            },
            create: {
                type: "POST",
                endpoint: "/admin/pageversions"
            },
            update: {
                type: "PATCH",
                endpoint: "/admin/pageversions/:id"
            },
            delete: {
                type: "DELETE",
                endpoint: "/admin/pageversions/:id"
            }
        },

        // Templates API endpoints
        templates: {
            get: {
                type: "GET",
                endpoint: "/admin/templates"
            },
            getById: {
                type: "GET",
                endpoint: "/admin/templates/:id"
            },
            create: {
                type: "POST",
                endpoint: "/admin/templates"
            },
            update: {
                type: "PATCH",
                endpoint: "/admin/templates/:id"
            },
            delete: {
                type: "DELETE",
                endpoint: "/admin/templates/:id"
            },
            getForTemplate: {
                type: "GET",
                endpoint: "/admin/templates/for-website"
            },
            addMedia: {
                type: "POST",
                endpoint: "/admin/templates/media"
            },
            updateMedia: {
                type: "PATCH",
                endpoint: "/admin/templates/media/:id"
            },
            createDuplicate: {
                type: "POST",
                endpoint: "/admin/templates/:id/copy"
            }
        },

        // Websites API endpoints
        websites: {
            get: {
                type: "GET",
                endpoint: `${userRole === "admin" ? "/admin" : ""}/websites`
            },
            getById: {
                type: "GET",
                endpoint: `${userRole === "admin" ? "/admin" : ""}/websites/:id`
            },
            create: {
                type: "POST",
                endpoint: "/admin/websites"
            },
            update: {
                type: "PATCH",
                endpoint: `${userRole === "admin" ? "/admin" : ""}/websites/:id`
            },
            delete: {
                type: "DELETE",
                endpoint: "/admin/websites/:id"
            },
            export: {
                type: "POST",
                endpoint: "/export/website/:id"
            },
            getByContentteam: {
                type: "GET",
                endpoint: "/admin/websites/content-team"
            }
        },

        // Users API endpoints
        users: {
            get: {
                type: "GET",
                endpoint: "/admin/users"
            },
            create: {
                type: "POST",
                endpoint: "/admin/users"
            },
            update: {
                type: "PATCH",
                endpoint: "/admin/users/:id"
            },
            delete: {
                type: "DELETE",
                endpoint: "/admin/users/:id"
            }
        },

        globalVarible: {
            get: {
                type: "GET",
                endpoint: "/admin/globalsettings"
            },
            create: {
                type: "POST",
                endpoint: "/admin/globalsettings"
            },
            update: {
                type: "PATCH",
                endpoint: "/admin/globalsettings/:id"
            },
            delete: {
                type: "DELETE",
                endpoint: "/admin/globalsettings/:id"
            }
        },

        // Auth API endpoints
        auth: {
            login: {
                type: "POST",
                endpoint: "/users/login"
            },
            getMe: {
                type: "GET",
                endpoint: "/users/me"
            },
            register: {
                type: "POST",
                endpoint: "/auth/register"
            }
        },

        // File upload endpoint
        upload: {
            image: {
                type: "POST",
                endpoint: "/upload"
            }
        }
    },
    FORM: {
        pageSetting: [
            {
                name: "name",
                label: "Page Name",
                required: true,
                tooltip: "The display name of your page",
                rules: [
                    { required: true, message: "Please enter page name" },
                    { min: 2, message: "Page name must be at least 2 characters" },
                    { max: 100, message: "Page name must be less than 100 characters" },
                ],
                placeholder: "e.g., Home, About, Contact",
                colProps: { xs: 24, md: 12 },
            },
            {
                name: "urlSlug",
                label: "URL Slug",
                required: true,
                tooltip:
                    "The URL path for this page (lowercase, no spaces). For home page, use 'home'.",
                rules: [
                    { required: true, message: "Please enter URL slug" },
                    {
                        pattern: /^[a-z0-9-_]+$/,
                        message:
                            "Only lowercase letters, numbers, hyphens and underscores allowed, spaces are not allowed",
                    },
                    // space not required
                    // { whitespace: true, message: "Spaces are not allowed" },
                ],
                placeholder: "e.g., home, about, contact",
                colProps: { xs: 24, md: 12 },
            },
            {
                name: "metaTitle",
                label: "Meta Title",
                required: false,
                tooltip:
                    "SEO title that appears in search results (recommended: 50-60 characters)",
                // rules: [{ max: 60, message: "Meta title should be under 60 characters" }],
                placeholder: "SEO title for this page",
                // showCount: true,
                // maxLength: 60,
                colProps: { xs: 24 },
            },
            {
                name: "metaDescription",
                label: "Meta Description",
                tooltip:
                    "Brief description for search engines (recommended: 150-160 characters)",
                type: "textarea",
                rules: [
                    { max: 160, message: "Meta description should be under 160 characters" },
                ],
                placeholder: "Brief description for search engines",
                rows: 3,
                showCount: true,
                maxLength: 160,
                colProps: { xs: 24 },
            },
            {
                name: "siteMapLabel",
                label: "Sitemap Label",
                tooltip: "Label for this page in the sitemap (used for SEO and navigation)",
                placeholder: "Enter sitemap label",
                colProps: { xs: 24 },
            },
            {
                name: "wrapperClass",
                label: "Wrapper Class",
                tooltip: "The class name for the wrapper element of this page",
                placeholder: "Enter wrapper class",
                colProps: { xs: 24 },
            },
            {
                name: "customCss",
                label: "Custom CSS",
                tooltip: "Add custom CSS for this page",
                type: "textarea",
                placeholder: "Enter custom CSS for this page",
                rows: 3,
                colProps: { xs: 24 },
            },
            {
                name: "customJs",
                label: "Custom JavaScript",
                tooltip: "Add custom JavaScript for this page",
                type: "textarea",
                placeholder: "Enter custom JavaScript for this page",
                rows: 3,
                colProps: { xs: 24 },
            },
            {
                name: "headers",
                label: "Custom Header",
                tooltip: "Add header tag for this page",
                type: "textarea",
                placeholder: "Enter header tag for this page",
                rows: 3,
                colProps: { xs: 24 },
            },
        ],
        userDetail: [
            {
                name: "name",
                label: "Username",
                required: true,
                type: "input",
                tooltip: "Unique identifier for the user",
                rules: [
                    { required: true, message: "Please enter username" },
                    // { min: 3, message: "Username must be at least 3 characters" },
                    { max: 50, message: "Username must be less than 50 characters" },
                    {
                        pattern: /^[a-zA-Z0-9_]+$/,
                        message: "Username can only contain letters, numbers, and underscores",
                    },
                ],
                placeholder: "Enter username",
                colProps: { xs: 24, md: 12 },
                component: "input",
            },
            {
                name: "email",
                label: "Email",
                type: "input",
                required: true,
                rules: [
                    { required: true, message: "Please enter email" },
                    { type: "email", message: "Please enter a valid email" },
                    { max: 100, message: "Email must be less than 100 characters" },
                ],
                placeholder: "Enter email address",
                colProps: { xs: 24, md: 12 },
                component: "input",
            },
            // Password (only in "add" mode)
            {
                name: "password",
                label: "Password",
                type: "password",
                required: true,
                extraProps: {
                    validateFirst: true,
                },
                rules: [
                    { required: true, message: "Please enter password" },
                    { min: 6, message: "Password must be at least 6 characters" },
                    { max: 128, message: "Password must be less than 128 characters" },

                    { pattern: /^\S*$/, message: "Password cannot contain spaces" },
                ],
                placeholder: "Enter password",
                colProps: { xs: 24, md: 12 },
                component: "password",
                // props: {
                //   iconRender: (visible: boolean) =>
                //     visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />,
                // },
                showIf: ({ mode }) => mode === "add",
            },
            // New Password (only in "edit" mode)
            {
                name: "password",
                label: "New Password",
                type: "password",
                required: false,
                rules: [
                    { min: 6, message: "Password must be at least 6 characters" },
                    { max: 128, message: "Password must be less than 128 characters" },
                    { pattern: /^\S*$/, message: "Password cannot contain spaces" },
                ],
                placeholder: "Enter new password (leave blank to keep current)",
                colProps: { xs: 24, md: 12 },
                component: "password",
                // props: {
                //   iconRender: (visible: boolean) =>
                //     visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />,
                // },
                showIf: ({ mode }) => mode === "edit",
            },
            {
                name: "role",
                label: "Role",
                required: true,
                type: "select",
                rules: [{ required: true, message: "Please select a role" }],
                placeholder: "Select role",
                colProps: { xs: 24, md: 12 },
                component: "select",
                options: [
                    { value: "admin", label: "Admin" },
                    { value: "content-team", label: "Content Team" },
                ],
            },
        ]
    },
    HEADER: {
        dashboard: {
            title: "Welcome back, Admin!",
            subtitle: "Manage your websites and components from your dashboard"
        },
        categories: {
            title: "Category Management",
            subtitle: "Organize your components into categories for better management"
        },
        addCategory: {
            title: "Add Category",
            subtitle: "Create a new category to organize your components"
        },
        editCategory: {
            title: "Edit Category",
            subtitle: "Modify category details and organization"
        },
        components: {
            title: "Component Library",
            subtitle: "Build and manage reusable components for your websites"
        },
        addComponent: {
            title: "Add Component",
            subtitle: "Build reusable components with HTML, CSS, and JavaScript"
        },
        editComponent: {
            title: "Edit Component",
            subtitle: "Easily customize and refine UI components."
        },
        pages: {
            title: "Page Builder",
            subtitle: "Create and manage pages with drag-and-drop components"
        },
        addPage: {
            title: "Create Page",
            subtitle: "Build your page using drag-and-drop components"
        },
        editPage: {
            title: "Edit Page",
            subtitle: "Edit your page using drag-and-drop components"
        },
        templates: {
            title: "Template Manager",
            subtitle: "Create and manage page templates for your websites"
        },
        addTemplate: {
            title: "Create Template",
            subtitle: "Group pages together to create reusable templates"
        },
        editTemplate: {
            title: "Edit Template",
            subtitle: "Edit your template with ease"
        },
        websites: {
            title: "Website Manager",
            subtitle: "Create, manage, and deploy your websites"
        },
        addWebsite: {
            title: "Create Website",
            subtitle: "Build a new website from your templates"
        },
        editWebsite: {
            title: "Edit Website",
            subtitle: "Modify website settings and configuration"
        },
        users: {
            title: "User Management",
            subtitle: "Manage user accounts and permissions"
        },
        addUser: {
            title: "Add User",
            subtitle: "Create a new user account with appropriate permissions"
        },
        editUser: {
            title: "Edit User",
            subtitle: "Modify user account details and permissions"
        },
        "activity-logs": {
            title: "Activity Log",
            subtitle: "Track all recent actions and system events."
        },
        "migration": {
            title: "Migration Interface",
            subtitle: "Seamlessly transfer your data and settings"
        },
        "global-variable": {
            title: "Global Variables",
            subtitle: "Manage and access variables anywhere"
        }
    }
};
