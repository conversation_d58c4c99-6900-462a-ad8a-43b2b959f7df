import React, { useState, useEffect, useCallback, useMemo } from "react";
import { Button, message, Tooltip } from "antd";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import useHttp from "../../../hooks/use-http";
import { CONSTANTS } from "../../../util/constant/CONSTANTS";
import TemplateLibrary from "./TemplateLibrary";
import TemplateStructure from "./TemplateStructure";
import PageSettingsModal from "./PageSettingsModal";
import { replaceTemplateContent } from "../../../util/functions";
import runtimeCache from "../../../utils/runtimeCache";
import CustomTooltip from "../../common/CustomTooltip";
import { ChevronLeft } from "lucide-react";

// DND Types
const DND_TYPES = {
  PAGE_ITEM: "PAGE_ITEM",
};

// Device sizes for responsive preview
const DEVICE_SIZES = {
  mobile: { width: 375, height: 667 },
  tablet: { width: 768, height: 1024 },
  laptop: { width: 1200, height: 800 },
};

const StructureTab = ({
  pagesData = {
    rows: [],
    count: 0,
  },
  components = [],
  formData,
  setFormData,
  template,
  onCancel,
  handleSubmit,
  saving,
  // pageList,
  // setPageList,
  reset,
  setReset,
  dynamicpages = {},
}) => {
  const api = useHttp();
  const [isPageLibraryOpen, setIsPageLibraryOpen] = useState(true);
  const [isTemplateStructureOpen, setIsTemplateStructureOpen] = useState(true);
  const [selectedPageId, setSelectedPageId] = useState(null);
  const [pageList, setPageList] = useState([]);
  const [screenSize, setScreenSize] = useState({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
  });
  const [reloadTrigger, setReloadTrigger] = useState(false);

  // Page Settings Modal state
  const [isPageSettingsModalVisible, setIsPageSettingsModalVisible] =
    useState(false);
  const [selectedPageForSettings, setSelectedPageForSettings] = useState(null);
  const [pageSettingsLoading, setPageSettingsLoading] = useState(false);

  useEffect(() => {
    setPageList((pr) => {
      const pagesList = [];

      formData?.pageData?.map((tmpPage) => {
        const pageObj = JSON.parse(JSON.stringify(tmpPage));
        if (pageObj?.type == "dynamic") {
          if (Object.keys(dynamicpages)?.length) {
            const page = dynamicpages[pageObj?.name];
            const sections = page?.sections;
            if (sections?.length) {
              sections?.map((el) => {
                const slugArr = el?.sectionItems;
                slugArr?.map((slugObj) => {
                  const dynamicPageObj = JSON.parse(JSON.stringify(pageObj));
                  const pageName = slugObj?.label;
                  pagesList?.push({
                    ...replaceTemplateContent({
                      page: dynamicPageObj,
                      tempContent: formData?.templateContentJSON,
                      content: formData?.contentJSON,
                      slug: slugObj,
                      mediaFiles: formData?.FileList,
                      // allVariables,
                    }),
                    url: `${dynamicPageObj.url}/${slugObj?.slug}`,
                    name: slugObj?.label,
                  });
                  // allVariables[pageName] = {
                  //   ...dynamicPageObj.variable,
                  // };
                });
              });
            }
          } else {
            pagesList?.push({
              ...replaceTemplateContent({
                page: pageObj,
                tempContent: formData?.templateContentJSON,
                content: formData?.contentJSON,
                mediaFiles: formData?.FileList,
                // allVariables,
              }),
              url: pageObj.url,
              name: pageObj.name,
            });
            // allVariables[pageObj.name] = { ...pageObj.variable };
          }
        } else {
          replaceTemplateContent({
            page: pageObj,
            tempContent: formData?.templateContentJSON,
            content: formData?.contentJSON,
            mediaFiles: formData?.FileList,
            // allVariables: allVariables,
          });
          // allVariables[pageObj.name] = { ...pageObj.variable };
          pagesList.push(pageObj);
        }
      });
      return pagesList;
    });
  }, [reset, formData]);

  // Responsive detection
  useEffect(() => {
    const checkScreenSize = () => {
      const width = window.innerWidth;
      setScreenSize({
        isMobile: width < 768,
        isTablet: width >= 768 && width < 1024,
        isDesktop: width >= 1024,
      });
    };

    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);
    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  // Global onChange handler for all component fields
  const handlePagesFieldChange = (index, field, value, extraData = {}) => {
    const updated = [...formData.pageData];
    updated[index] = { ...updated[index], [field]: value, ...extraData };
    // console.log("Updated pages:", updated);
    setFormData((prevData) => ({
      ...prevData,
      pageData: updated,
      // pageComponentList: updated,
    }));
  };
  // console.log(formData, "formData");
  // Remove page from template
  const removePageFromTemplate = (pageId) => {
    const updatedPages =
      formData.pageData?.filter((p) => p?.id != pageId) || [];
    setFormData((pr) => ({
      ...pr,
      pageData: updatedPages,
    }));
    // If the removed page was selected, clear selection
    if (selectedPageId === pageId) {
      setSelectedPageId(null);
    }
  };

  // Reorder helper for immediate component moves
  const moveComponent = (fromIndex, toIndex) => {
    // console.log(`Moving component from ${fromIndex} to ${toIndex}`);
    const updatedComponents = [...formData?.pageData];
    const [movedComponent] = updatedComponents.splice(fromIndex, 1);
    updatedComponents.splice(toIndex, 0, movedComponent);

    setFormData((prevData) => ({
      ...prevData,
      pageData: updatedComponents,
      // pageData: updatedComponents,
    }));
  };

  // Update page in template
  const updatePageInTemplate = (pageId, updates) => {
    const updatedPages =
      formData.pageData?.map((p) =>
        p.id === pageId ? { ...p, ...updates } : p
      ) || [];
    setFormData({ ...formData, pageData: updatedPages });
  };

  // Save template structure
  const handleSaveStructure = async () => {
    try {
      // Generate full template content from current structure
      const fullTemplateContent = generateTemplateHTML();
      // Create templateComponentList from current pageData
      // const templateComponentList =
      //   formData.pageData?.map((page, index) => ({
      //     ...page,
      //     version: "v1",
      //     url: page.slug || `/${page.name.toLowerCase().replace(/\s+/g, "-")}`,
      //     repeator: "single",
      //     position: index,
      //     showNavbar: page.showNavbar || false,
      //     navPosition: page.navPosition || index,
      //     placeHolder: page.pagePlaceHolder || [],
      //   })) || [];

      // Update formData with generated content
      const updatedFormData = {
        ...formData,
        full_template_content: fullTemplateContent,
        // templateComponentList: templateComponentList,
        // content: formData.content || [],
      };
      handleSubmit(updatedFormData);
    } catch (error) {
      message.error("Failed to generate template structure. Please try again.");
    }
  };

  // Generate template HTML from current structure
  const generateTemplateHTML = () => {
    if (!formData?.pageData || formData?.pageData?.length === 0) {
      return '<div class="template-placeholder">No pages added to template</div>';
    }

    const pagesHTML = formData?.pageData
      .map((page, index) => {
        return `
        <div class="template-page" data-page-id="${
          page?.id
        }" data-position="${index}">
          <div class="page-content">
            ${
              page?.full_page_content ||
              `<div class="page-placeholder">Page: ${page?.name}</div>`
            }
          </div>
        </div>
      `;
      })
      .join("\n");

    return `
      <div class="template-container">
        <div class="template-pages">
          ${pagesHTML}
        </div>
      </div>
    `;
  };

  // Get filtered pages (exclude already added pages)
  const availablePages = pagesData?.rows?.filter(
    (page) => !formData?.pageData?.some((tp) => tp.id == page.id)
  );
  // console.log(pagesData, "pagesData", formData);
  // Page Settings Modal handlers
  const handleOpenPageSettings = (page, index) => {
    setSelectedPageForSettings({ ...page, index });
    setIsPageSettingsModalVisible(true);
  };

  const handleClosePageSettings = () => {
    setIsPageSettingsModalVisible(false);
    setSelectedPageForSettings(null);
    setPageSettingsLoading(false);
  };

  const handleSavePageSettings = async (updatedSettings) => {
    setPageSettingsLoading(true);
    try {
      // Update the page in formData.pages
      const pageIndex = selectedPageForSettings.index;
      const updatedPages = [...formData.pageData];
      updatedPages[pageIndex] = {
        ...updatedPages[pageIndex],
        ...updatedSettings,
      };

      setFormData((prevData) => ({
        ...prevData,
        pageData: updatedPages,
      }));

      handleClosePageSettings();
    } catch (error) {
      console.error("Error saving page settings:", error);
      setPageSettingsLoading(false);
      throw error; // Re-throw to let modal handle the error
    }
  };

  // Add page to template function
  const addPageToTemplate = (page, selectedVersionId = null) => {
    const newPage = {
      ...page,
      order: formData.pageData?.length || 0,
      version: selectedVersionId || page?.pageversions?.[0]?.id || "v1",
      type: page.type || "static",
      showNavbar: false,
      // navIndex: 0,
      navbarIndex: 0,
      url: page.url || `/${page.name?.toLowerCase().replace(/\s+/g, "-")}`,
    };

    const updatedPages = [...(formData.pageData || []), newPage];
    setFormData((prevData) => ({
      ...prevData,
      pageData: updatedPages,
    }));

    message.success(`${page.name} added to template`);
  };

  // State management bridge for drag-and-drop with caching
  // Generate template ID for caching
  const templateId = useMemo(() => {
    return template?.id ? `template_${template.id}` : "new_template";
  }, [template?.id]);

  // Get used page IDs for filtering
  const usedPageIds = useMemo(() => {
    return formData?.pageData?.map((page) => page.id) || [];
  }, [formData?.pageData]);

  // Callback handlers for page drag-and-drop operations
  const handlePageDragStart = useCallback((page) => {
    // console.log("Page drag started in StructureTab:", page.name);
    // Could add loading states or other UI feedback here
  }, []);

  const handlePageDragEnd = useCallback(
    (page, wasDropped) => {
      // console.log(
      //   "Page drag ended in StructureTab:",
      //   page.name,
      //   "Dropped:",
      //   wasDropped
      // );
      if (wasDropped) {
        // Invalidate cache to ensure fresh data on next load
        runtimeCache.updateTemplatePageAvailability(page.id, false, templateId);
      }
    },
    [templateId]
  );

  const handlePageAdded = useCallback(
    (page) => {
      // console.log("Page added to template:", page.name);
      // Update cache to reflect page is no longer available
      runtimeCache.updateTemplatePageAvailability(page.id, false, templateId);
      message.success(`${page.name} added to template`);
    },
    [templateId]
  );

  const handlePageRemoved = useCallback(
    (page) => {
      // console.log("Page removed from template:", page.name);
      // Update cache to reflect page is now available
      runtimeCache.updateTemplatePageAvailability(page.id, true, templateId);
      message.success(`${page.name} removed from template`);
    },
    [templateId]
  );

  const onCancelHandler = () => {
    onCancel();
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="tw-h-screen tw-flex tw-overflow-hidden tw-relative">
        {/* Mobile Backdrop */}
        {screenSize?.isMobile &&
          (isPageLibraryOpen || isTemplateStructureOpen) && (
            <div
              className="tw-fixed tw-inset-0 tw-bg-black tw-bg-opacity-50 tw-z-40"
              onClick={() => {
                setIsPageLibraryOpen(false);
                setIsTemplateStructureOpen(false);
              }}
            />
          )}

        <TemplateLibrary
          isPageLibraryOpen={isPageLibraryOpen}
          setIsPageLibraryOpen={setIsPageLibraryOpen}
          screenSize={screenSize}
          availablePages={availablePages}
          onAddPageToTemplate={addPageToTemplate}
          usedPageIds={usedPageIds}
          templateId={templateId}
          onPageDragStart={handlePageDragStart}
          onPageDragEnd={handlePageDragEnd}
          setReloadTrigger={setReloadTrigger}
          reloadTrigger={reloadTrigger}
        />

        {/* Center Template Structure Area */}
        <div className="tw-flex-1 tw-flex tw-flex-col tw-bg-gray-50 tw-overflow-hidden">
          {/* Header */}
          <div className="tw-bg-white tw-border-b tw-border-gray-200  ">
            <div className="tw-flex tw-items-center tw-justify-between">
              <div className=" tw-border-gray-200 tw-flex tw-items-center tw-justify-between ">
                {screenSize?.isMobile ||
                  (!isPageLibraryOpen && (
                    <div className="tw-flex tw-h-full tw-py-6 tw-items-center tw-justify-center tw-border-r tw-border-gray-200">
                      <CustomTooltip title="Show Page Library">
                        <button
                          onClick={() => setIsPageLibraryOpen(true)}
                          className="tw-text-gray-600 tw-px-3 tw-flex tw-items-center tw-justify-center tw-rounded-lg"
                        >
                          <ChevronLeft size={30} className="tw-rotate-180" />
                        </button>
                      </CustomTooltip>
                    </div>
                  ))}
                <div className="tw-flex tw-items-start  tw-flex-col tw-pl-6 tw-py-4">
                  <h2 className="tw-text-lg tw-font-semibold tw-text-gray-900">
                    Template Structure
                  </h2>
                  <span className="tw-text-sm tw-text-gray-500">
                    {formData?.pageData?.length || 0} pages
                  </span>
                </div>
              </div>

              <div className="tw-flex tw-items-center tw-space-x-3 tw-pr-6 tw-py-4">
                <Button onClick={onCancelHandler} className="tw-px-6 tw-h-10">
                  Cancel
                </Button>
                <Button
                  type="primary"
                  size="large"
                  onClick={handleSaveStructure}
                  disabled={saving}
                  className="tw-px-6 tw-h-10 tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
                >
                  {saving ? "Saving..." : "Save Template"}
                </Button>
              </div>
            </div>
          </div>

          {/* Right Sidebar - Template Structure */}
          <div className="tw-flex-1 tw-overflow-auto">
            <TemplateStructure
              isTemplateStructureOpen={isTemplateStructureOpen}
              setIsTemplateStructureOpen={setIsTemplateStructureOpen}
              pageData={formData}
              formData={formData}
              setFormData={setFormData}
              removePageFromTemplate={removePageFromTemplate}
              onComponentFieldChange={handlePagesFieldChange}
              moveComponent={moveComponent}
              screenSize={screenSize}
              templateId={templateId}
              onPageRemoved={handlePageRemoved}
              onPageAdded={handlePageAdded}
              reloadTrigger={reloadTrigger}
              setReloadTrigger={setReloadTrigger}
            />
          </div>
        </div>

        {/* Page Settings Modal */}
        <PageSettingsModal
          visible={isPageSettingsModalVisible}
          onCancel={handleClosePageSettings}
          onSave={handleSavePageSettings}
          pageData={selectedPageForSettings}
          loading={pageSettingsLoading}
        />
      </div>
    </DndProvider>
  );
};

export default StructureTab;

// for safe one dynamic page content ============
// "templateContentJSON": {
//     "buses-d": {
//       "hero_section_home_page": {
//         "hero_section_title": "${slug}",
//         "hero_section_description": "${${slug}_hero_section_description}"
//       },
//       "buspage_bus_details": {
//         "bus-image": "${slug}_bus_image",
//         "bus_title": "${slug}_bus_title",
//         "bus_description": "${${slug}_bus_description}"
//     },
//     "bus_interior_view": {
//         "bus-interior-image-1": "${slug}-bus_interior_image_1",
//         "bus-interior-image-2": "${slug}-bus_interior_image_2",
//         "bus-interior-image-3": "${slug}-bus_interior_image_3"
//     }
//     }
//   }
//   "buses-d":{
//     "hero_section_content": {
//        "50-passenger-charter-bus-rental_hero_section_description": "A 50-passenger charter bus rental is one of the best ways to travel with large groups in Clifton, New Jersey. For groups who want the highest quality transportation, the charter bus rentals in our network can be equipped with premium leather seating so that every passenger can stretch out and relax while on the road. And, there’s plenty of storage space available for luggage to ensure your group won’t have to play Tetris jamming everyone’s bags and personal belongings into the seats. Or perhaps overhead storage for easy access to personal items like books and electronic devices. In addition to plenty of storage and legroom, your charter bus will include the services of a trained driver throughout your entire New Jersey trip."
//     },
//     "common_buttons": {
//         "quote_url": "/get-quote",
//         "get_quote_button_text": "Get 30-Second Online Quote",
//         "contact_number": "/tel:2014640115",
//         "contact_number_button_label": "Call Us: ************"
//     },
//     "buspage_bus_details": {
//         "50-passenger-charter-bus-rental_bus_title": "50 Passenger Charter Bus Amenities",
//         "50-passenger-charter-bus-rental_bus_image": "_img-clifton-50-passenger-charter-bus",
//         "50-passenger-charter-bus-rental_bus_description": "<p>The 50-passenger charter buses in the Bus Rental Company Clifton network include several amenities to make sure everyone is comfortable during your trip. Enjoy A/C on hot days, reclining seats and extra legroom when you want to take a nap, and overhead bins for keeping any belongings with you. You’ll find TVs with DVD players to pass the time and power outlets for charging devices while you travel with free WiFi access to stream videos, movies, and music, too. If you need a bus rental with an onboard lavatory for long trips around New Jersey, we’ll work with you to find the best accommodations, including premium cloth or leather seats upon your request. Additional amenities may also be available like reading lights and storage bays beneath the bus. Simply call our team at&nbsp;<strong>************</strong>&nbsp;during your booking process, and we’ll find a 50-passenger charter bus rental to accommodate every passenger while seeing if we can equip your bus rental with all the amenities you desire like upgraded interiors. We’d love the opportunity to be your transportation provider, whether that involves a&nbsp;<a href=\"https://busrentalcompanyclifton.com/union-city-bus-rental/\">charter bus rental in Union City</a>, a&nbsp;<a href=\"https://busrentalcompanyclifton.com/paterson-bus-rental/\">shuttle bus rental in Paterson</a>, or&nbsp;<a href=\"https://busrentalcompanyclifton.com/bloomfield-bus-rental/\">Bloomfield shuttle bus rentals</a>.</p>"   },
//     "bus_amenities": {
//         "amenities_title": "Available Amenities Can Include"
//     },
//     "bus_interior_view": {
//         "50-passenger-charter-bus-rental-bus_interior_image_1": "clifton-50-passenger-charter-bus-rental",
//         "50-passenger-charter-bus-rental-bus_interior_image_2": "clifton-50-passenger-charter-bus-interior",
//         "50-passenger-charter-bus-rental-bus_interior_image_3": "clifton-50-passenger-charter-bus-inside"
//     },
//     "common_cta_quote_today": {
//         "cta_quote_title": "Get A Quote Today!",
//         "quote_button_related_text": "See Pictures & Prices in Seconds",
//         "quote_url": "/get-quote",
//         "get_quote_button_text": "Get 30-Second Online Quote",
//         "call_button_related_text": "Live agents available 24/7/365!",
//         "contact_number": "/tel:2014640115",
//         "contact_number_button_label": "Call Us: ************"
//     },
//     "testimonial": {
//         "background-pattern-bg-image": "background-pattern",
//         "testimonial_title": "What Our Customers Say",
//         "testimonial_cards": [
//           {
//             "user-image": "natalia-g",
//             "user_name": "Natalia G.",
//             "testimonial": "We used the service for a company off-site. Everything went according to plan, with no issues at all."
//           },
//           {
//             "user-image": "joo-p",
//             "user_name": "João P.",
//             "testimonial": "Booked for a private celebration with a large group. The entire experience was smooth and timely."
//           },
//           {
//             "user-image": "emma-k",
//             "user_name": "Emma K.",
//             "testimonial": "Used for a wedding guest shuttle, and the process was seamless from start to finish. Highly recommend."
//           },
//           {
//             "user-image": "oskar-d",
//             "user_name": "Oskar D.",
//             "testimonial": "Hired for a sports event day trip. Communication was clear, and everything ran exactly on time."
//           }
//         ]
//     },
//     "h2_content_title": {
//         "content_title": "Some of Our Other Buses"
//     },
//     "bus_grid_list": {
//         "buses_list_heading": "",
//         "busesList": [
//           {
//             "bus_price_range": "$180 – $500+ per hour",
//             "bus-image": "54-passenger-charter-bus-clifton",
//             "bus-interior": "passenger-charter-bus-interior-clifton",
//             "bus_title": "54 Passenger Charter Bus",
//             "bus_view_button_text": "View This Bus",
//             "quote_url": "/get-quote/",
//             "get_quote_button_text": "Get a Quote",
//             "view_Bus_Url": "/54-passenger-charter-bus-rental"
//           },
//           {
//             "bus_price_range": "$180 – $500+ per hour",
//             "bus-image": "55-passenger-charter-bus-clifton",
//             "bus-interior": "passenger-charter-bus-interior-clifton",
//             "bus_title": "55 Passenger Charter Bus",
//             "bus_view_button_text": "View This Bus",
//             "quote_url": "/get-quote/",
//             "get_quote_button_text": "Get a Quote",
//             "view_Bus_Url": "/55-passenger-charter-bus-rental"
//           },
//           {
//             "bus_price_range": "$180 – $500+ per hour",
//             "bus-image": "56-passenger-charter-bus-clifton",
//             "bus-interior": "passenger-charter-bus-interior-clifton",
//             "bus_title": "56 Passenger Charter Bus",
//             "bus_view_button_text": "View This Bus",
//             "quote_url": "/get-quote/",
//             "get_quote_button_text": "Get a Quote",
//             "view_Bus_Url": "/56-passenger-charter-bus-rental"
//           },
//           {
//             "bus_price_range": "$150 – $430+ per hour",
//             "bus-image": "15-passenger-minibus-clifton",
//             "bus-interior": "passenger-charter-bus-interior-clifton",
//             "bus_title": "15 Passenger Minibus",
//             "bus_view_button_text": "View This Bus",
//             "quote_url": "/get-quote/",
//             "get_quote_button_text": "Get a Quote",
//             "view_Bus_Url": "/15-passenger-minibus-rental"
//           },
//           {
//             "bus_price_range": "$150 – $430+ per hour",
//             "bus-image": "18-passenger-minibus-clifton",
//             "bus-interior": "passenger-charter-bus-interior-clifton",
//             "bus_title": "18 Passenger Minibus",
//             "bus_view_button_text": "View This Bus",
//             "quote_url": "/get-quote/",
//             "get_quote_button_text": "Get a Quote",
//             "view_Bus_Url": "/18-passenger-minibus-rental"
//           },
//           {
//             "bus_price_range": "$150 – $430+ per hour",
//             "bus-image": "20-passenger-minibus-clifton",
//             "bus-interior": "passenger-charter-bus-interior-clifton",
//             "bus_title": "20 Passenger Mini bus",
//             "bus_view_button_text": "View This Bus",
//             "quote_url": "/get-quote/",
//             "get_quote_button_text": "Get a Quote",
//             "view_Bus_Url": "/20-passenger-minibus-rental"
//           },
//           {
//             "bus_price_range": "$150 – $450+ per hour",
//             "bus-image": "25-passenger-minibus-clifton",
//             "bus-interior": "passenger-charter-bus-interior-clifton",
//             "bus_title": "25 Passenger Mini bus",
//             "bus_view_button_text": "View This Bus",
//             "quote_url": "/get-quote/",
//             "get_quote_button_text": "Get a Quote",
//             "view_Bus_Url": "/25-passenger-minibus-rental"
//           },
//           {
//             "bus_price_range": "$150 – $450+ per hour",
//             "bus-image": "28-passenger-minibus-clifton",
//             "bus-interior": "passenger-charter-bus-interior-clifton",
//             "bus_title": "28 Passenger Mini bus",
//             "bus_view_button_text": "View This Bus",
//             "quote_url": "/get-quote/",
//             "get_quote_button_text": "Get a Quote",
//             "view_Bus_Url": "/28-passenger-minibus-rental"
//           },
//           {
//             "bus_price_range": "$150 – $450+ per hour",
//             "bus-image": "30-passenger-minibus-clifton",
//             "bus-interior": "passenger-charter-bus-interior-clifton",
//             "bus_title": "30 Passenger Minibus",
//             "bus_view_button_text": "View This Bus",
//             "quote_url": "/get-quote/",
//             "get_quote_button_text": "Get a Quote",
//             "view_Bus_Url": "/30-passenger-minibus-rental"
//           },
//           {
//             "bus_price_range": "$150 – $450+ per hour",
//             "bus-image": "35-passenger-minibus-clifton",
//             "bus-interior": "passenger-charter-bus-interior-clifton",
//             "bus_title": "35 Passenger Minibus",
//             "bus_view_button_text": "View This Bus",
//             "quote_url": "/get-quote/",
//             "get_quote_button_text": "Get a Quote",
//             "view_Bus_Url": "/35-passenger-minibus-rental"
//           },
//           {
//             "bus_price_range": "$155 – $450+ per hour",
//             "bus-image": "sprinter-van-with-driver-clifton",
//             "bus-interior": "sprinter-van-with-driver-interior-clifton",
//             "bus_title": "Sprinter Van Rental With Driver",
//             "bus_view_button_text": "View This Bus",
//             "quote_url": "/get-quote/",
//             "get_quote_button_text": "Get a Quote",
//             "view_Bus_Url": "/sprinter-van-rental-with-driver"
//           },
//           {
//             "bus_price_range": "$145 – $450+ per hour",
//             "bus-image": "school-bus-rental-clifton",
//             "bus-interior": "school-bus-rental-interior-clifton",
//             "bus_title": "School Bus",
//             "bus_view_button_text": "View This Bus",
//             "quote_url": "/get-quote/",
//             "get_quote_button_text": "Get a Quote",
//             "view_Bus_Url": "/school-bus-rental"
//           },
//           {
//             "bus_price_range": "$180 - $450+ per hour",
//             "bus-image": "party-bus-rental-clifton",
//             "bus-interior": "party-bus-rental-interior-clifton",
//             "bus_title": "Party Bus",
//             "bus_view_button_text": "View This Bus",
//             "quote_url": "/get-quote/",
//             "get_quote_button_text": "Get a Quote",
//             "view_Bus_Url": "/party-bus-rental"
//           },
//           {
//             "bus_price_range": "$180 - $450+ per hour",
//             "bus-image": "sprinter-limo-rental-clifton",
//             "bus-interior": "sprinter-limo-rental-interior-clifton",
//             "bus_title": "Sprinter Limo",
//             "bus_view_button_text": "View This Bus",
//             "quote_url": "/get-quote/",
//             "get_quote_button_text": "Get a Quote",
//             "view_Bus_Url": "/sprinter-limo-rental"
//           }
//         ]
//     },
//     "common_cta_section": {
//         "cta_title": "Get a Quote in 30 Seconds",
//         "contact_number": "/tel:2014640115",
//         "contact_number_button_label": "Call Us: ************",
//         "quote_url": "/get-quote",
//         "get_quote_button_text": "Get 30-Second Online Quote",
//         "cta-review-photo": "review-photo",
//         "available_24_7_text": "We’re available everyday 24/7/365!",
//         "cta-bus-rental-image": "clifton-bus-rental"

// }
//     }
