import React, { useEffect } from "react";
import { Modal, Form, Input, Select, Button } from "antd";
import useHttp from "../../../hooks/use-http";
import { CONSTANTS } from "../../../util/constant/CONSTANTS";

const { Option } = Select;

const DuplicateWebsiteModal = ({ visible, onCancel, website, onSuccess }) => {
  const [form] = Form.useForm();
  const api = useHttp();

  useEffect(() => {
    if (website) {
      form.setFieldsValue({
        name: `${website.name} (Copy)`,
        version: website.version,
      });
    }
  }, [website, form]);

  const handleFinish = (values) => {
    const payload = {
      ...values,
      templateId: website.template.id,
    };
    api.sendRequest(
      CONSTANTS.API.websites.post,
      (res) => {
        onSuccess(res);
        onCancel();
      },
      payload
    );
  };

  return (
    <Modal
      title="Duplicate Website"
      visible={visible}
      onCancel={onCancel}
      footer={null}
      destroyOnClose
    >
      <Form form={form} layout="vertical" onFinish={handleFinish}>
        <Form.Item
          name="name"
          label="Website Name"
          rules={[{ required: true, message: "Please enter the website name" }]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          name="version"
          label="Version"
          rules={[{ required: true, message: "Please select a version" }]}
        >
          <Select>
            <Option value="V1">V1</Option>
            <Option value="V2">V2</Option>
          </Select>
        </Form.Item>
        <div className="tw-flex tw-justify-end tw-gap-2">
          <Button onClick={onCancel}>Cancel</Button>
          <Button type="primary" htmlType="submit" loading={api.isLoading}>
            Create
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default DuplicateWebsiteModal;
