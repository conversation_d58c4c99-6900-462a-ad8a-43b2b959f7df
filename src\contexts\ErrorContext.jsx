import React, { createContext, useContext, useState, useCallback } from "react";
import { notification } from "antd";

const ErrorContext = createContext();

export const useError = () => {
  const context = useContext(ErrorContext);
  if (!context) {
    throw new Error("useError must be used within an ErrorProvider");
  }
  return context;
};

export const ErrorProvider = ({ children }) => {
  const [errors, setErrors] = useState([]);
  const [isMaintenanceMode, setIsMaintenanceMode] = useState(false);
  const [maintenanceMessage, setMaintenanceMessage] = useState("");

  // Add error to the error list
  const addError = useCallback((error) => {
    const errorWithId = {
      id: Date.now() + Math.random(),
      timestamp: new Date().toISOString(),
      ...error,
    };

    setErrors((prev) => [...prev, errorWithId]);

    // Show notification based on error type
    if (error.showNotification !== false) {
      switch (error.type) {
        case "network":
          notification.error({
            message: "Network Error",
            description: error.message || "Unable to connect to the server",
            duration: 5,
          });
          break;
        // case "server":
        //   notification.error({
        //     message: "Server Error",
        //     description: error.message || "Something went wrong on our end",
        //     duration: 5,
        //   });
        //   break;
        case "validation":
          notification.warning({
            message: "Validation Error",
            description: error.message || "Please check your input",
            duration: 3,
          });
          break;
        case "auth":
          notification.error({
            message: "Authentication Error",
            description: error.message || "Please log in again",
            duration: 5,
          });
          break;
        default:
        // notification.error({
        //   message: "Error",
        //   description: error.message || "An unexpected error occurred",
        //   duration: 3,
        // });
      }
    }

    return errorWithId.id;
  }, []);

  // Remove error by ID
  const removeError = useCallback((errorId) => {
    setErrors((prev) => prev.filter((error) => error.id !== errorId));
  }, []);

  // Clear all errors
  const clearErrors = useCallback(() => {
    setErrors([]);
  }, []);

  // Clear errors by type
  const clearErrorsByType = useCallback((type) => {
    setErrors((prev) => prev.filter((error) => error.type !== type));
  }, []);

  // Get errors by type
  const getErrorsByType = useCallback(
    (type) => {
      return errors.filter((error) => error.type === type);
    },
    [errors]
  );

  // Get latest error
  const getLatestError = useCallback(() => {
    return errors.length > 0 ? errors[errors.length - 1] : null;
  }, [errors]);

  // Set maintenance mode
  const setMaintenance = useCallback((isMaintenance, message = "") => {
    setIsMaintenanceMode(isMaintenance);
    setMaintenanceMessage(message);

    if (isMaintenance) {
      notification.warning({
        message: "Maintenance Mode",
        description: message || "The system is currently under maintenance",
        duration: 0, // Don't auto-close
      });
    }
  }, []);

  // Handle HTTP errors
  const handleHttpError = useCallback(
    (error, response) => {
      const errorData = {
        type: "http",
        status: response?.status,
        statusText: response?.statusText,
        message: error.message || "HTTP request failed",
        url: response?.config?.url,
        method: response?.config?.method,
        timestamp: new Date().toISOString(),
      };

      // Categorize error by status code
      if (response?.status >= 500) {
        errorData.type = "server";
        errorData.message = "Server error. Please try again later.";
      } else if (response?.status === 404) {
        errorData.type = "notFound";
        errorData.message = "The requested resource was not found.";
      } else if (response?.status === 403) {
        errorData.type = "forbidden";
        errorData.message =
          "You do not have permission to access this resource.";
      } else if (response?.status === 401) {
        errorData.type = "auth";
        errorData.message = "Authentication required. Please log in again.";
      }
      // else if (response?.status >= 400) {
      //   errorData.type = "client";
      //   errorData.message = "Invalid request. Please check your input.";
      // }

      return addError(errorData);
    },
    [addError]
  );

  // Handle network errors
  const handleNetworkError = useCallback(
    (error) => {
      return addError({
        type: "network",
        message:
          "Network connection failed. Please check your internet connection.",
        originalError: error,
      });
    },
    [addError]
  );

  // Handle validation errors
  const handleValidationError = useCallback(
    (errors) => {
      const errorMessages = Array.isArray(errors) ? errors.join(", ") : errors;

      return addError({
        type: "validation",
        message: errorMessages,
        showNotification: true,
      });
    },
    [addError]
  );

  // Get error statistics
  const getErrorStats = useCallback(() => {
    const stats = {
      total: errors.length,
      byType: {},
      recent: errors.filter(
        (error) =>
          Date.now() - new Date(error.timestamp).getTime() < 24 * 60 * 60 * 1000
      ).length,
    };

    errors.forEach((error) => {
      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
    });

    return stats;
  }, [errors]);

  const value = {
    // State
    errors,
    isMaintenanceMode,
    maintenanceMessage,

    // Error management
    addError,
    removeError,
    clearErrors,
    clearErrorsByType,
    getErrorsByType,
    getLatestError,

    // Specialized handlers
    handleHttpError,
    handleNetworkError,
    handleValidationError,

    // Maintenance
    setMaintenance,

    // Statistics
    getErrorStats,
  };

  return (
    <ErrorContext.Provider value={value}>{children}</ErrorContext.Provider>
  );
};

export default ErrorContext;
