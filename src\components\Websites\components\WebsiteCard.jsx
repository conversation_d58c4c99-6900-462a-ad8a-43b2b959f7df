import React from "react";
import { <PERSON>, <PERSON>, But<PERSON>, Dropdown, <PERSON><PERSON><PERSON>, Popconfirm } from "antd";
import {
  MoreVertical,
  Copy,
  Trash2,
  Edit,
  Download,
  Eye,
  CopyPlus,
  Edit2,
} from "lucide-react";
import CommonButton from "../../common/CommonButton";
import Title from "antd/es/typography/Title";
import Paragraph from "antd/es/typography/Paragraph";

const WebsiteCard = ({
  website,
  onDuplicate,
  onDelete,
  onEdit,
  onPreview,
  onExport,
  isLoading = false,
}) => {
  const { name, template, status, version, websiteversions } = website;
  // const [operationLoading, setOperationLoading] = useState({});
  const templateDetail = websiteversions?.[0]?.templateversion || {};
  const menu = (
    <div className="tw-flex tw-gap-y-2">
      <Button
        type="text"
        icon={<CopyPlus size={16} />}
        onClick={() => onDuplicate(website)}
        className="tw-w-full tw-text-left"
      />

      {/* <Button
        type="text"
        icon={<Eye size={16} />}
        onClick={() => onPreview(website)}
        className="tw-w-full tw-text-left"
      >
        Preview
      </Button> */}
      <Button
        type="text"
        icon={<Edit2 size={16} />}
        onClick={() => onEdit(website)}
        className="tw-w-full tw-text-left"
      />
      <Tooltip title="Delete Template" key="delete">
        <Popconfirm
          title="Delete Template"
          description="Are you sure you want to delete this template?"
          onConfirm={() => onDelete(website?.id)}
          okText="Yes"
          cancelText="No"
          okButtonProps={{
            danger: true,
            loading: isLoading,
          }}
          disabled={isLoading}
        >
          <Button
            type="text"
            danger
            icon={<Trash2 size={16} />}
            onClick={() => onDelete(website.id)}
            className="tw-w-full tw-text-left"
          />
        </Popconfirm>
      </Tooltip>
    </div>
  );

  return (
    <Card className="tw-shadow-sm hover:tw-shadow-md tw-transition-shadow">
      <div className="tw-flex tw-justify-between tw-items-start">
        <div>
          <Tag
            className="tw-rounded-xl"
            color={status === "draft" ? "default" : "green"}
          >
            {status}
          </Tag>
          <Tag color="default" className="tw-rounded-xl tw-bg-white">
            v{websiteversions?.[0]?.version || 0}
          </Tag>
        </div>
        <div>{menu}</div>
        {/* <Dropdown overlay={menu} trigger={["click"]}>
          <Button type="text" icon={<MoreVertical size={20} />} />
        </Dropdown> */}
      </div>
      <div className="tw-mt-4 tw-gap-y-5">
        <div>
          <Title level={4} className="!tw-mb-0 tw-text-gray-900 tw-truncate">
            {name}
          </Title>
        </div>
        {templateDetail?.template?.name && (
          <Paragraph
            ellipsis={{ rows: 2, expandable: false }}
            className="tw-text-gray-600 tw-text-sm tw-mb-2 tw-mt-2"
          >
            Template Name: {templateDetail?.template?.name}
          </Paragraph>
        )}
      </div>
      <div className="tw-mt-6 tw-text-right tw-flex tw-items-center tw-justify-between">
        <p className="tw-text-gray-500">
          Template Version: v{templateDetail?.version}
        </p>
        <CommonButton
          onClick={() => onExport(website)}
          disabled={status == "draft"}
          icon={<Download className="tw-w-4 tw-h-4 tw-mr-2" />}
          text="Export"
        />
      </div>
    </Card>
  );
};

export default WebsiteCard;
