# Task: Implement Website Manager Page

## Objective

- Create a page to display, search, and manage websites.
- Implement features for creating, duplicating, editing, and deleting websites.
- Ensure the design is consistent with the provided Figma mockups.

## UI task list

- [ ] Create the main layout for the Website Manager page.
- [ ] Implement the header section with the title and "Create Website" button.
- [ ] Add a search bar for filtering websites.
- [ ] Create a card component to display website information.
- [ ] Implement a grid layout to display the website cards.
- [ ] Integrate a scroll pagination component to handle large lists of websites.
- [ ] Create a modal for duplicating a website.
- [ ] Add action buttons (duplicate, edit, delete) to the website cards.
- [ ] Implement the preview functionality using Ant Design components.

## API task list

- [ ] Integrate GET all websites API | GET /websites
- [ ] Integrate Create website API | POST /websites
- [ ] Integrate Duplicate website API | POST /websites/:id/duplicate
- [ ] Integrate Update website API | PATCH /websites/:id
- [ ] Integrate Delete website API | DELETE /websites/:id
