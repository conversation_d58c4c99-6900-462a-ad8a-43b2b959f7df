import React from "react";
import { Form, Input, Select, Space, Tooltip, Typography } from "antd";
import { Info } from "lucide-react";

const { Text } = Typography;
const { TextArea } = Input;

const FormFields = ({ field }) => {
  const {
    name,
    label,
    required = false,
    tooltip,
    rules = [],
    type = "input", // input | textarea | select | password
    colProps = {}, // if you want to wrap with <Col> outside
    extraProps = { validateFirst: true },
    ...rest
  } = field;

  const mergedRules = required
    ? [
        // { required: true, message: `Please enter ${label.toLowerCase()}` },
        ...rules,
      ]
    : rules;

  let control;
  switch (type) {
    case "input":
      control = <Input className="tw-rounded-lg" {...rest} />;
      break;
    case "textarea":
      control = <TextArea className="tw-rounded-lg" {...rest} />;
      break;
    case "select":
      control = <Select className="tw-rounded-lg" {...rest} />;
      break;
    case "password":
      control = <Input.Password className="tw-rounded-lg" {...rest} />;
      break;
    default:
      control = <Input className="tw-rounded-lg" {...rest} />;
  }

  return (
    <Form.Item
      name={name}
      className="tw-mb-4"
      rules={mergedRules}
      label={
        <Space className="tw-items-center">
          <Text strong>{label}</Text>
          {/* {required && (
            <Text type="danger" className="tw-ml-0">
              *
            </Text>
          )} */}
          {tooltip && (
            <Tooltip title={tooltip}>
              <Info className="tw-w-3 tw-h-3 tw-text-gray-400" />
            </Tooltip>
          )}
        </Space>
      }
      {...extraProps}
    >
      {control}
    </Form.Item>
  );
};

export default FormFields;
