import { Button } from "antd";
import React from "react";

const CommonButton = (props) => {
  return (
    <Button
      type="primary"
      size="large"
      onClick={() => {
        navigate("/components/add");
        setShowEditor(true);
      }}
      //   icon={<Plus className="tw-w-4 tw-h-4 tw-mr-2" />}
      className="tw-px-6 tw-h-10 tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
      {...props}
    >
      {props?.text}
    </Button>
  );
};

export default CommonButton;
