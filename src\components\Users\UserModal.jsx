import React, { useState, useEffect } from "react";
import {
  Modal,
  Form,
  Input,
  Select,
  Button,
  Space,
  Typography,
  message,
} from "antd";
import { EyeInvisibleOutlined, EyeTwoTone } from "@ant-design/icons";
import useHttp from "../../hooks/use-http";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import FormFields from "../common/FormFields";

const { Title } = Typography;
const { Option } = Select;

const UserModal = ({ visible, mode, user, onClose, onSave }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const api = useHttp();

  useEffect(() => {
    if (visible && user && mode !== "add") {
      form.setFieldsValue({
        name: user.name,
        email: user.email,
        role: user.role,
      });
    } else if (visible && mode === "add") {
      form.resetFields();
    }
  }, [visible, user, mode, form]);

  // const handleSubmit = async (values) => {
  //   setLoading(true);

  //   try {
  //     if (mode === "add") {
  //       await api.sendRequest(
  //         CONSTANTS.API.users.create,
  //         (res) => {
  //           message.success("User created successfully!");
  //           onSave();
  //         },
  //         values,
  //         null,
  //         // "User created successfully!",
  //         (error) => {
  //           message.error(error || "Failed to create user");
  //         }
  //       );
  //     } else if (mode === "edit") {
  //       await api.sendRequest(
  //         {
  //           ...CONSTANTS.API.users.update,
  //           endpoint: `/admin/users/${user.id}`,
  //         },
  //         (res) => {
  //           message.success("User updated successfully!");
  //           onSave();
  //         },
  //         values,
  //         null,
  //         // "User updated successfully!",
  //         (error) => {
  //           message.error(error || "Failed to update user");
  //         }
  //       );
  //     }
  //   } catch (error) {
  //     message.error("An error occurred");
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  const handleSubmit = async () => {
    try {
      const allValues = form.getFieldsValue(true); // all form fields
      const touchedValues = form.getFieldsValue(); // only current values

      // Only keep touched fields
      const changedValues = {};
      Object.keys(allValues).forEach((key) => {
        if (form.isFieldTouched(key)) {
          changedValues[key] = allValues[key];
        }
      });

      if (mode === "add") {
        await api.sendRequest(
          CONSTANTS.API.users.create,
          () => {
            message.success("User created successfully!");
            onSave();
          },
          allValues
        );
      } else if (mode === "edit") {
        if (Object.keys(changedValues).length === 0) {
          message.info("No changes detected");
          return;
        }

        await api.sendRequest(
          {
            ...CONSTANTS.API.users.update,
            endpoint: `/admin/users/${user.id}`,
          },
          () => {
            message.success("User updated successfully!");
            onSave();
          },
          changedValues // 🔥 only changed fields
        );
      }
    } catch (error) {
      message.error("An error occurred");
    } finally {
      setLoading(false);
    }
  };

  const getModalTitle = () => {
    switch (mode) {
      case "add":
        return "Add New User";
      case "edit":
        return "Edit User";
      case "view":
        return "View User";
      default:
        return "User";
    }
  };

  const isReadOnly = mode === "view";

  const closeHandler = () => {
    form.resetFields();
    onClose();
  };

  return (
    <Modal
      title={getModalTitle()}
      open={visible}
      centered
      onCancel={closeHandler}
      footer={null}
      width={500}
      destroyOnClose
      className="tw-max-w-full tw-mx-4"
      style={{ maxWidth: "90vw" }}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        disabled={isReadOnly}
        size="large"
      >
        {CONSTANTS.FORM.userDetail.map((field) =>
          field?.showIf ? (
            field?.showIf({ mode }) && (
              <FormFields key={field.name} field={field} />
            )
          ) : (
            <FormFields key={field.name} field={field} />
          )
        )}

        {!isReadOnly && (
          <Form.Item style={{ marginBottom: 0 }}>
            <Space style={{ width: "100%", justifyContent: "flex-end" }}>
              <Button disabled={loading} onClick={closeHandler}>
                Cancel
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                className="tw-px-6 tw-h-10 tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
                loading={loading}
                // className="tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-border-none tw-hover:tw-from-blue-700 tw-hover:tw-to-purple-700"
              >
                {mode === "add" ? "Create User" : "Update User"}
              </Button>
            </Space>
          </Form.Item>
        )}

        {isReadOnly && (
          <Form.Item style={{ marginBottom: 0 }}>
            <Space style={{ width: "100%", justifyContent: "flex-end" }}>
              <Button disabled={loading} onClick={closeHandler}>
                Close
              </Button>
            </Space>
          </Form.Item>
        )}
      </Form>
    </Modal>
  );
};

export default UserModal;
